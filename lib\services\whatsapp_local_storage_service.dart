import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:crypto/crypto.dart';
import 'package:path/path.dart' as path;
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'session_service.dart';

/// WhatsApp-like local storage service for MR Garments
///
/// Directory structure (like WhatsApp):
/// Android/media/
/// └── com.mrgarments/
///     └── media/
///         └── Images/
///             ├── Sent/      # Images sent by user
///             └── Private/   # Images received from others
///
/// Features:
/// - Encrypted file naming using SHA256 hash
/// - All images converted to .jpg format
/// - Automatic thumbnail generation
/// - Firebase cleanup after receiver storage
class WhatsAppLocalStorageService {
  static WhatsAppLocalStorageService? _instance;
  static WhatsAppLocalStorageService get instance =>
      _instance ??= WhatsAppLocalStorageService._();
  WhatsAppLocalStorageService._();

  static Directory? _senderDir;
  static Directory? _receiverDir;
  static Directory? _tempDir;
  static bool _initialized = false;

  /// Initialize WhatsApp-like local storage directories
  static Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Request storage permissions
      await _requestStoragePermissions();

      // Get Android media directory path (like WhatsApp)
      Directory mediaDir;
      try {
        // Get external storage directory first
        final externalDir = await getExternalStorageDirectory();
        if (externalDir != null) {
          // Navigate to Android/media directory like WhatsApp
          // Path: /storage/emulated/0/Android/media/
          final androidPath = externalDir.path.split('/Android/data/')[0];
          mediaDir = Directory('$androidPath/Android/media');
        } else {
          // Fallback to application documents directory
          mediaDir = await getApplicationDocumentsDirectory();
        }
      } catch (e) {
        // Fallback to application documents directory
        mediaDir = await getApplicationDocumentsDirectory();
      }

      // Create com.mrgarments directory structure like WhatsApp (com.whatsapp)
      final appPackageDir = Directory(
        path.join(mediaDir.path, 'com.mrgarments'),
      );
      if (!await appPackageDir.exists()) {
        await appPackageDir.create(recursive: true);
      }

      // Create media directory inside com.mrgarments
      final appMediaDir = Directory(path.join(appPackageDir.path, 'media'));
      if (!await appMediaDir.exists()) {
        await appMediaDir.create(recursive: true);
      }

      // Create Images directory inside media (like WhatsApp Images folder)
      final imagesDir = Directory(path.join(appMediaDir.path, 'Images'));
      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
      }

      // Create Sent directory inside Images (like WhatsApp Sent folder)
      _senderDir = Directory(path.join(imagesDir.path, 'Sent'));
      if (!await _senderDir!.exists()) {
        await _senderDir!.create(recursive: true);
      }

      // Create Private directory inside Images (like WhatsApp Private folder)
      _receiverDir = Directory(path.join(imagesDir.path, 'Received'));
      if (!await _receiverDir!.exists()) {
        await _receiverDir!.create(recursive: true);
      }

      // Create temp directory for processing
      final appDir = await getApplicationDocumentsDirectory();
      _tempDir = Directory(path.join(appDir.path, 'whatsapp_temp'));
      if (!await _tempDir!.exists()) {
        await _tempDir!.create(recursive: true);
      }

      _initialized = true;
      debugPrint('✅ WhatsApp Local Storage initialized successfully');
      debugPrint('📁 Sender: ${_senderDir!.path}');
      debugPrint('📁 Receiver: ${_receiverDir!.path}');
    } catch (e) {
      debugPrint('❌ Error initializing WhatsApp Local Storage: $e');
      rethrow;
    }
  }

  /// Request necessary storage permissions
  static Future<void> _requestStoragePermissions() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;

        if (androidInfo.version.sdkInt >= 30) {
          // Android 11+ - Request MANAGE_EXTERNAL_STORAGE
          if (!await Permission.manageExternalStorage.isGranted) {
            final status = await Permission.manageExternalStorage.request();
            if (!status.isGranted) {
              throw Exception('External storage permission denied');
            }
          }
        } else {
          // Android 10 and below
          if (!await Permission.storage.isGranted) {
            final status = await Permission.storage.request();
            if (!status.isGranted) {
              throw Exception('Storage permission denied');
            }
          }
        }
      }
    } catch (e) {
      debugPrint('❌ Permission request error: $e');
      rethrow;
    }
  }

  /// Generate filename based on image content hash (for deduplication)
  static Future<String> _generateContentBasedFileName(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final digest = sha256.convert(bytes);
      return '${digest.toString().substring(0, 16)}.jpg';
    } catch (e) {
      // Fallback to timestamp-based name if content reading fails
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final digest = sha256.convert(utf8.encode(timestamp.toString()));
      return '${digest.toString().substring(0, 16)}.jpg';
    }
  }

  /// Check if image already exists in storage
  static Future<String?> _findExistingImage(String contentHash) async {
    try {
      // Check in sender directory
      final senderPath = path.join(_senderDir!.path, contentHash);
      if (await File(senderPath).exists()) {
        return senderPath;
      }

      // Check in receiver directory
      final receiverPath = path.join(_receiverDir!.path, contentHash);
      if (await File(receiverPath).exists()) {
        return receiverPath;
      }

      return null;
    } catch (e) {
      debugPrint('❌ Error checking existing image: $e');
      return null;
    }
  }

  /// Store image as sender (when sending images) with WhatsApp-like deduplication
  static Future<SenderImageResult> storeImageAsSender({
    required File sourceFile,
    required String messageId,
    required String chatId,
  }) async {
    try {
      if (!_initialized) await initialize();

      final userId = await SessionService.getUserId();
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Generate content-based filename for deduplication
      final contentBasedFileName = await _generateContentBasedFileName(
        sourceFile,
      );

      // Check if this exact image already exists
      final existingImagePath = await _findExistingImage(contentBasedFileName);

      if (existingImagePath != null) {
        // Image already exists, return existing paths
        final existingFile = File(existingImagePath);
        final imageSize = await existingFile.length();

        // Check if thumbnail exists
        final thumbnailPath = path.join(
          path.dirname(existingImagePath),
          'thumb_$contentBasedFileName',
        );
        final thumbnailFile = File(thumbnailPath);
        final thumbnailExists = await thumbnailFile.exists();
        final thumbnailSize =
            thumbnailExists ? await thumbnailFile.length() : 0;

        debugPrint('♻️ Using existing sender image: $contentBasedFileName');

        return SenderImageResult(
          localImagePath: existingImagePath,
          thumbnailPath: thumbnailExists ? thumbnailPath : null,
          encryptedFileName: contentBasedFileName,
          imageSize: imageSize,
          thumbnailSize: thumbnailSize,
          success: true,
        );
      }

      // Image doesn't exist, store new copy
      final senderPath = path.join(_senderDir!.path, contentBasedFileName);
      final compressedFile = await _compressAndConvertToJpg(
        sourceFile,
        senderPath,
      );

      final imageSize = await compressedFile.length();

      // Only generate thumbnail for larger images (> 500KB) like WhatsApp
      String? thumbnailPath;
      int thumbnailSize = 0;

      if (imageSize > 500 * 1024) {
        // 500KB threshold
        thumbnailPath = path.join(
          _senderDir!.path,
          'thumb_$contentBasedFileName',
        );
        final thumbnailFile = await _generateThumbnail(
          compressedFile,
          thumbnailPath,
        );
        thumbnailSize = await thumbnailFile.length();
      }

      debugPrint(
        '📤 New sender image stored: $contentBasedFileName ($imageSize bytes)',
      );

      return SenderImageResult(
        localImagePath: compressedFile.path,
        thumbnailPath: thumbnailPath,
        encryptedFileName: contentBasedFileName,
        imageSize: imageSize,
        thumbnailSize: thumbnailSize,
        success: true,
      );
    } catch (e) {
      debugPrint('❌ Error storing sender image: $e');
      return SenderImageResult(success: false, error: e.toString());
    }
  }

  /// Store image as receiver (when receiving images) with WhatsApp-like deduplication
  static Future<ReceiverImageResult> storeImageAsReceiver({
    required String downloadUrl,
    required String messageId,
    required String chatId,
    required String senderId,
  }) async {
    try {
      if (!_initialized) await initialize();

      final currentUserId = await SessionService.getUserId();
      if (currentUserId == null) {
        throw Exception('User not authenticated');
      }

      // Generate content-based filename first to check for existing image
      final tempFileName = 'temp_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final tempPath = path.join(_tempDir!.path, tempFileName);

      File? downloadedFile;
      String? contentBasedFileName;

      try {
        // Download image to temp directory first to get content hash
        downloadedFile = await _downloadImage(downloadUrl, tempPath);

        // Generate content-based filename for deduplication
        contentBasedFileName = await _generateContentBasedFileName(
          downloadedFile,
        );

        // Check if this exact image already exists
        final existingImagePath = await _findExistingImage(
          contentBasedFileName,
        );

        if (existingImagePath != null) {
          // Image already exists, clean up temp file and return existing paths
          await _safeDeleteFile(downloadedFile);

          final existingFile = File(existingImagePath);
          final imageSize = await existingFile.length();

          // Check if thumbnail exists
          final thumbnailPath = path.join(
            path.dirname(existingImagePath),
            'thumb_$contentBasedFileName',
          );
          final thumbnailFile = File(thumbnailPath);
          final thumbnailExists = await thumbnailFile.exists();
          final thumbnailSize =
              thumbnailExists ? await thumbnailFile.length() : 0;

          debugPrint('♻️ Using existing receiver image: $contentBasedFileName');

          return ReceiverImageResult(
            localImagePath: existingImagePath,
            thumbnailPath: thumbnailExists ? thumbnailPath : null,
            encryptedFileName: contentBasedFileName,
            imageSize: imageSize,
            thumbnailSize: thumbnailSize,
            success: true,
          );
        }

        // Image doesn't exist, store new copy in receiver directory
        final receiverPath = path.join(
          _receiverDir!.path,
          contentBasedFileName,
        );

        // Compress and store the image
        final compressedFile = await _compressAndConvertToJpg(
          downloadedFile,
          receiverPath,
        );

        final imageSize = await compressedFile.length();

        // Only generate thumbnail for larger images (> 500KB) like WhatsApp
        String? thumbnailPath;
        int thumbnailSize = 0;

        if (imageSize > 500 * 1024) {
          // 500KB threshold
          thumbnailPath = path.join(
            _receiverDir!.path,
            'thumb_$contentBasedFileName',
          );
          final thumbnailFile = await _generateThumbnail(
            compressedFile,
            thumbnailPath,
          );
          thumbnailSize = await thumbnailFile.length();
        }

        debugPrint('📥 Receiver storage successful: $contentBasedFileName');
        debugPrint('📁 Receiver path: ${compressedFile.path}');

        return ReceiverImageResult(
          localImagePath: compressedFile.path,
          thumbnailPath: thumbnailPath,
          encryptedFileName: contentBasedFileName,
          imageSize: imageSize,
          thumbnailSize: thumbnailSize,
          success: true,
        );
      } finally {
        // Always clean up temp file in finally block
        if (downloadedFile != null) {
          await _safeDeleteFile(downloadedFile);
        }
      }
    } catch (e) {
      debugPrint('❌ Error storing receiver image: $e');
      return ReceiverImageResult(success: false, error: e.toString());
    }
  }

  /// Safely delete a file without throwing exceptions
  static Future<void> _safeDeleteFile(File file) async {
    try {
      if (await file.exists()) {
        await file.delete();
        debugPrint('🗑️ Temp file cleaned up: ${file.path}');
      }
    } catch (e) {
      debugPrint('⚠️ Could not delete temp file ${file.path}: $e');
    }
  }

  /// Download image from URL
  static Future<File> _downloadImage(String url, String savePath) async {
    try {
      final response = await HttpClient().getUrl(Uri.parse(url));
      final httpResponse = await response.close();

      final file = File(savePath);
      final sink = file.openWrite();
      await httpResponse.pipe(sink);
      await sink.close();

      return file;
    } catch (e) {
      throw Exception('Failed to download image: $e');
    }
  }

  /// Compress image and convert to JPG format
  static Future<File> _compressAndConvertToJpg(
    File sourceFile,
    String targetPath, {
    int quality = 85,
  }) async {
    try {
      final compressedFile = await FlutterImageCompress.compressAndGetFile(
        sourceFile.path,
        targetPath,
        quality: quality,
        minWidth: 1024,
        minHeight: 1024,
        format: CompressFormat.jpeg, // Force JPG format
        keepExif: false,
      );

      if (compressedFile != null) {
        return File(compressedFile.path);
      } else {
        // If compression fails, copy and rename to .jpg
        final jpgPath =
            targetPath.endsWith('.jpg')
                ? targetPath
                : '${targetPath.split('.').first}.jpg';
        return await sourceFile.copy(jpgPath);
      }
    } catch (e) {
      // Fallback: copy and rename to .jpg
      final jpgPath =
          targetPath.endsWith('.jpg')
              ? targetPath
              : '${targetPath.split('.').first}.jpg';
      return await sourceFile.copy(jpgPath);
    }
  }

  /// Generate thumbnail for quick preview
  static Future<File> _generateThumbnail(
    File sourceFile,
    String thumbnailPath, {
    int size = 200,
  }) async {
    try {
      final thumbnailFile = await FlutterImageCompress.compressAndGetFile(
        sourceFile.path,
        thumbnailPath,
        quality: 60,
        minWidth: size,
        minHeight: size,
        format: CompressFormat.jpeg,
        keepExif: false,
      );

      if (thumbnailFile != null) {
        return File(thumbnailFile.path);
      } else {
        return await sourceFile.copy(thumbnailPath);
      }
    } catch (e) {
      return await sourceFile.copy(thumbnailPath);
    }
  }

  /// Check if local file exists (sender or receiver)
  static Future<bool> localFileExists(String? localPath) async {
    if (localPath == null || localPath.isEmpty) return false;
    try {
      final file = File(localPath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// Get local file if it exists
  static Future<File?> getLocalFile(String? localPath) async {
    if (await localFileExists(localPath)) {
      return File(localPath!);
    }
    return null;
  }

  /// Delete local image and thumbnail (sender or receiver)
  static Future<bool> deleteLocalImage(
    String? localPath,
    String? thumbnailPath,
  ) async {
    try {
      bool success = true;

      if (localPath != null && await localFileExists(localPath)) {
        await File(localPath).delete();
        debugPrint('🗑️ Deleted local image: ${path.basename(localPath)}');
      }

      if (thumbnailPath != null && await localFileExists(thumbnailPath)) {
        await File(thumbnailPath).delete();
        debugPrint('🗑️ Deleted thumbnail: ${path.basename(thumbnailPath)}');
      }

      return success;
    } catch (e) {
      debugPrint('❌ Error deleting local image: $e');
      return false;
    }
  }

  /// Clean up old files (older than specified days)
  static Future<void> cleanupOldFiles({int daysOld = 30}) async {
    try {
      if (!_initialized) await initialize();

      final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));

      // Clean sender directory
      if (_senderDir != null && await _senderDir!.exists()) {
        await _cleanupDirectory(_senderDir!, cutoffDate);
      }

      // Clean receiver directory
      if (_receiverDir != null && await _receiverDir!.exists()) {
        await _cleanupDirectory(_receiverDir!, cutoffDate);
      }

      // Clean temp directory
      if (_tempDir != null && await _tempDir!.exists()) {
        await _cleanupDirectory(_tempDir!, cutoffDate);
      }

      debugPrint('🧹 WhatsApp local storage cleanup completed');
    } catch (e) {
      debugPrint('❌ Error during cleanup: $e');
    }
  }

  /// Clean up files in a directory older than cutoff date
  static Future<void> _cleanupDirectory(
    Directory dir,
    DateTime cutoffDate,
  ) async {
    try {
      final files = dir.listSync();
      int deletedCount = 0;

      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            await file.delete();
            deletedCount++;
          }
        }
      }

      if (deletedCount > 0) {
        debugPrint('🧹 Cleaned $deletedCount files from ${dir.path}');
      }
    } catch (e) {
      debugPrint('❌ Error cleaning directory ${dir.path}: $e');
    }
  }

  /// Get storage info for both sender and receiver directories
  static Future<WhatsAppStorageInfo> getStorageInfo() async {
    try {
      if (!_initialized) await initialize();

      int senderSize = 0;
      int senderCount = 0;
      int receiverSize = 0;
      int receiverCount = 0;

      // Get sender directory info
      if (_senderDir != null && await _senderDir!.exists()) {
        final senderInfo = await _getDirectorySize(_senderDir!);
        senderSize = senderInfo.size;
        senderCount = senderInfo.count;
      }

      // Get receiver directory info
      if (_receiverDir != null && await _receiverDir!.exists()) {
        final receiverInfo = await _getDirectorySize(_receiverDir!);
        receiverSize = receiverInfo.size;
        receiverCount = receiverInfo.count;
      }

      final totalSize = senderSize + receiverSize;
      final totalCount = senderCount + receiverCount;

      return WhatsAppStorageInfo(
        senderSize: senderSize,
        senderCount: senderCount,
        receiverSize: receiverSize,
        receiverCount: receiverCount,
        totalSize: totalSize,
        totalCount: totalCount,
        formattedSenderSize: _formatBytes(senderSize),
        formattedReceiverSize: _formatBytes(receiverSize),
        formattedTotalSize: _formatBytes(totalSize),
      );
    } catch (e) {
      return WhatsAppStorageInfo(
        senderSize: 0,
        senderCount: 0,
        receiverSize: 0,
        receiverCount: 0,
        totalSize: 0,
        totalCount: 0,
        formattedSenderSize: '0 B',
        formattedReceiverSize: '0 B',
        formattedTotalSize: '0 B',
      );
    }
  }

  /// Get directory size and file count
  static Future<DirectoryInfo> _getDirectorySize(Directory dir) async {
    int size = 0;
    int count = 0;

    try {
      final files = dir.listSync(recursive: true);
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          size += stat.size;
          count++;
        }
      }
    } catch (e) {
      debugPrint('❌ Error getting directory size: $e');
    }

    return DirectoryInfo(size: size, count: count);
  }

  /// Format bytes to human readable string
  static String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Clear all cached files (both sender and receiver)
  static Future<bool> clearAllCache() async {
    try {
      if (!_initialized) await initialize();

      // Clear sender directory
      if (_senderDir != null && await _senderDir!.exists()) {
        await _senderDir!.delete(recursive: true);
        await _senderDir!.create(recursive: true);
      }

      // Clear receiver directory
      if (_receiverDir != null && await _receiverDir!.exists()) {
        await _receiverDir!.delete(recursive: true);
        await _receiverDir!.create(recursive: true);
      }

      // Clear temp directory
      if (_tempDir != null && await _tempDir!.exists()) {
        await _tempDir!.delete(recursive: true);
        await _tempDir!.create(recursive: true);
      }

      debugPrint('🧹 All WhatsApp local storage cleared');
      return true;
    } catch (e) {
      debugPrint('❌ Error clearing cache: $e');
      return false;
    }
  }

  /// Get sender directory path
  static String? get senderDirectoryPath => _senderDir?.path;

  /// Get receiver directory path
  static String? get receiverDirectoryPath => _receiverDir?.path;

  /// Clean up duplicate images (maintenance function)
  /// This can be called periodically to remove duplicate images that were stored before deduplication
  static Future<void> cleanupDuplicateImages() async {
    try {
      if (!_initialized) await initialize();

      debugPrint('🧹 Starting duplicate image cleanup...');

      final Map<String, List<File>> contentHashGroups = {};
      int duplicatesRemoved = 0;
      int spaceSaved = 0;

      // Process sender directory
      if (_senderDir != null && await _senderDir!.exists()) {
        await _processDuplicatesInDirectory(_senderDir!, contentHashGroups);
      }

      // Process receiver directory
      if (_receiverDir != null && await _receiverDir!.exists()) {
        await _processDuplicatesInDirectory(_receiverDir!, contentHashGroups);
      }

      // Remove duplicates, keeping the first occurrence
      for (final entry in contentHashGroups.entries) {
        final files = entry.value;
        if (files.length > 1) {
          // Keep the first file, remove the rest
          for (int i = 1; i < files.length; i++) {
            final file = files[i];
            final size = await file.length();
            await file.delete();
            duplicatesRemoved++;
            spaceSaved += size;

            // Also remove corresponding thumbnail if exists
            final thumbnailPath = path.join(
              path.dirname(file.path),
              'thumb_${path.basename(file.path)}',
            );
            final thumbnailFile = File(thumbnailPath);
            if (await thumbnailFile.exists()) {
              final thumbSize = await thumbnailFile.length();
              await thumbnailFile.delete();
              spaceSaved += thumbSize;
            }
          }
        }
      }

      debugPrint(
        '🧹 Cleanup complete: Removed $duplicatesRemoved duplicates, saved ${(spaceSaved / 1024 / 1024).toStringAsFixed(2)} MB',
      );
    } catch (e) {
      debugPrint('❌ Error during cleanup: $e');
    }
  }

  /// Process duplicates in a specific directory
  static Future<void> _processDuplicatesInDirectory(
    Directory dir,
    Map<String, List<File>> contentHashGroups,
  ) async {
    try {
      final files =
          dir
              .listSync()
              .whereType<File>()
              .where(
                (f) =>
                    f.path.endsWith('.jpg') &&
                    !path.basename(f.path).startsWith('thumb_'),
              )
              .toList();

      for (final file in files) {
        try {
          final bytes = await file.readAsBytes();
          final digest = sha256.convert(bytes);
          final contentHash = digest.toString().substring(0, 16);

          contentHashGroups.putIfAbsent(contentHash, () => []).add(file);
        } catch (e) {
          debugPrint('❌ Error processing file ${file.path}: $e');
        }
      }
    } catch (e) {
      debugPrint('❌ Error processing directory ${dir.path}: $e');
    }
  }
}

/// Result of storing an image as sender
class SenderImageResult {
  final String? localImagePath;
  final String? thumbnailPath;
  final String? encryptedFileName;
  final int? imageSize;
  final int? thumbnailSize;
  final bool success;
  final String? error;

  SenderImageResult({
    this.localImagePath,
    this.thumbnailPath,
    this.encryptedFileName,
    this.imageSize,
    this.thumbnailSize,
    required this.success,
    this.error,
  });
}

/// Result of storing an image as receiver
class ReceiverImageResult {
  final String? localImagePath;
  final String? thumbnailPath;
  final String? encryptedFileName;
  final int? imageSize;
  final int? thumbnailSize;
  final bool success;
  final String? error;

  ReceiverImageResult({
    this.localImagePath,
    this.thumbnailPath,
    this.encryptedFileName,
    this.imageSize,
    this.thumbnailSize,
    required this.success,
    this.error,
  });
}

/// WhatsApp storage information
class WhatsAppStorageInfo {
  final int senderSize;
  final int senderCount;
  final int receiverSize;
  final int receiverCount;
  final int totalSize;
  final int totalCount;
  final String formattedSenderSize;
  final String formattedReceiverSize;
  final String formattedTotalSize;

  WhatsAppStorageInfo({
    required this.senderSize,
    required this.senderCount,
    required this.receiverSize,
    required this.receiverCount,
    required this.totalSize,
    required this.totalCount,
    required this.formattedSenderSize,
    required this.formattedReceiverSize,
    required this.formattedTotalSize,
  });
}

/// Directory information
class DirectoryInfo {
  final int size;
  final int count;

  DirectoryInfo({required this.size, required this.count});
}
