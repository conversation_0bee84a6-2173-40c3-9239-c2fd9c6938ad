import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/models/chat.dart';
import 'package:mr_garments_mobile/providers/chat_provider.dart';
import 'package:mr_garments_mobile/screens/chat/create_group_page.dart';
import 'package:mr_garments_mobile/screens/chat/member_chat_inbox.dart';
import 'package:mr_garments_mobile/services/session_service.dart';

class GroupsPage extends ConsumerWidget {
  const GroupsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final groupChatsAsync = ref.watch(groupChatsStreamProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      body: groupChatsAsync.when(
        data: (groupChats) {
          if (groupChats.isEmpty) {
            return _buildEmptyState(context);
          }
          return _buildGroupsList(groupChats, context);
        },
        loading:
            () => const Center(
              child: CircularProgressIndicator(color: Color(0xFF005368)),
            ),
        error: (error, stack) => _buildErrorState(error.toString()),
      ),
      floatingActionButton: FutureBuilder<String?>(
        future: SessionService.getUserRole(),
        builder: (context, snapshot) {
          final userRole = snapshot.data;
          if (userRole == 'admin') {
            return FloatingActionButton(
              onPressed: () => _navigateToCreateGroup(context),
              backgroundColor: const Color(0xFF005368),
              child: const Icon(LucideIcons.plus, color: Colors.white),
            );
          }
          return const SizedBox.shrink(); // Hide for non-admin users
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            LucideIcons.users,
            size: 80,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No Groups Yet',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first group to start collaborating',
            style: GoogleFonts.poppins(fontSize: 16, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          FutureBuilder<String?>(
            future: SessionService.getUserRole(),
            builder: (context, snapshot) {
              final userRole = snapshot.data;
              if (userRole == 'admin') {
                return ElevatedButton.icon(
                  onPressed: () => _navigateToCreateGroup(context),
                  icon: const Icon(LucideIcons.plus, color: Colors.white),
                  label: Text(
                    'Create Group',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF005368),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                );
              }
              return Text(
                'Only admin can create groups',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            LucideIcons.alertCircle,
            size: 80,
            color: Colors.red.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Error Loading Groups',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.red[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildGroupsList(List<Chat> groupChats, BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        // Refresh will be handled automatically by the stream
      },
      color: const Color(0xFF005368),
      child: ListView.builder(
        padding: const EdgeInsets.fromLTRB(16, 4, 16, 8),
        itemCount: groupChats.length,
        itemBuilder: (context, index) {
          final groupChat = groupChats[index];
          return _buildGroupChatTile(groupChat, context);
        },
      ),
    );
  }

  Widget _buildGroupChatTile(Chat groupChat, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        onTap: () => _navigateToGroupChat(groupChat, context),
        contentPadding: const EdgeInsets.all(12),
        leading: CircleAvatar(
          radius: 25,
          backgroundColor: const Color(0xFF005368),
          backgroundImage:
              groupChat.groupImageUrl != null
                  ? NetworkImage(groupChat.groupImageUrl!)
                  : null,
          child:
              groupChat.groupImageUrl == null
                  ? Text(
                    (groupChat.groupName?.isNotEmpty == true)
                        ? groupChat.groupName![0].toUpperCase()
                        : 'G',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 18,
                    ),
                  )
                  : null,
        ),
        title: Text(
          groupChat.groupName ?? 'Unknown Group',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
            fontSize: 16,
            color: const Color(0xFF005368),
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (groupChat.groupDescription?.isNotEmpty == true)
              Text(
                groupChat.groupDescription!,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            const SizedBox(height: 4),
            Row(
              children: [
                Text(
                  '${groupChat.memberIds.length} members',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[500],
                  ),
                ),
                if (groupChat.lastMessage != null) ...[
                  const SizedBox(width: 8),
                  const Text('•', style: TextStyle(color: Colors.grey)),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getLastMessageText(groupChat.lastMessage!),
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (groupChat.lastMessageTime != null)
              Text(
                _formatTime(groupChat.lastMessageTime!),
                style: GoogleFonts.poppins(
                  fontSize: 11,
                  color: Colors.grey[500],
                ),
              ),
            const SizedBox(height: 4),
            Icon(LucideIcons.chevronRight, color: Colors.grey[400], size: 16),
          ],
        ),
      ),
    );
  }

  String _getLastMessageText(dynamic lastMessage) {
    if (lastMessage == null) return '';

    // Handle both Message object and Map
    if (lastMessage is Map<String, dynamic>) {
      final text = lastMessage['text'] as String?;
      final type = lastMessage['type'] as String?;

      if (text?.isNotEmpty == true) {
        return text!;
      }

      switch (type) {
        case 'image':
          return '📷 Image';
        case 'file':
          return '📎 File';
        case 'audio':
          return '🎵 Audio';
        case 'video':
          return '🎥 Video';
        default:
          return 'Message';
      }
    }

    return lastMessage.toString();
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }

  void _navigateToCreateGroup(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CreateGroupPage()),
    );
  }

  void _navigateToGroupChat(Chat groupChat, BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => MemberChatInbox(
              chatId: groupChat.id,
              chatName: groupChat.groupName ?? 'Group Chat',
              isGroup: true,
            ),
      ),
    );
  }
}
