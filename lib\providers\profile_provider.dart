import 'dart:io';
import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/services/profile_service.dart';
import 'package:mr_garments_mobile/services/session_service.dart';

class ProfileProvider with ChangeNotifier {
  Map<String, dynamic>? _userData;
  bool _isLoading = false;
  String? _error;

  Map<String, dynamic>? get userData => _userData;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Load current user profile data
  Future<void> loadProfile() async {
    _setLoading(true);
    _error = null;

    try {
      final data = await ProfileService.getCurrentProfile();
      _userData = data['user'];
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  /// Update user profile
  Future<bool> updateProfile({
    required String name,
    required String email,
    String? mobileNumber,
    String? address,
    File? profileImage,
  }) async {
    _setLoading(true);
    _error = null;

    try {
      String? imageUrl;
      
      // Upload image if provided
      if (profileImage != null) {
        imageUrl = await ProfileService.uploadProfileImage(profileImage);
      }

      // Update profile
      final response = await ProfileService.updateProfile(
        name: name,
        email: email,
        mobileNumber: mobileNumber,
        address: address,
        profileImageUrl: imageUrl ?? _userData?['profile_image_url'],
      );

      // Update local user data
      if (response['user'] != null) {
        _userData = response['user'];
        notifyListeners();
      }

      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Change user password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    _setLoading(true);
    _error = null;

    try {
      await ProfileService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
        newPasswordConfirmation: confirmPassword,
      );
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Upload profile image only
  Future<String?> uploadProfileImage(File imageFile) async {
    _setLoading(true);
    _error = null;

    try {
      final imageUrl = await ProfileService.uploadProfileImage(imageFile);
      return imageUrl;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Clear error message
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// Initialize with session data
  void initializeWithSessionData() async {
    final userData = await SessionService.getUserData();
    if (userData != null) {
      _userData = userData;
      notifyListeners();
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
