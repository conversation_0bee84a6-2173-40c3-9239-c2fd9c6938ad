import 'dart:ui';

import 'package:flutter/material.dart';

class GlassmorphicCard extends StatelessWidget {
  final Widget child;
  final double? width;
  final EdgeInsetsGeometry padding;
  const GlassmorphicCard({
    required this.child,
    this.width,
    this.padding = const EdgeInsets.all(24),
    super.key
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 12.0, sigmaY: 12.0),
          child: Container(
            width: width ?? MediaQuery.of(context).size.width * 0.88,
            padding: padding,
            decoration: BoxDecoration(
              color: const Color(0xFFF8F8F8).withAlpha(128),
              borderRadius: BorderRadius.circular(25),
              border: Border.all(color: Colors.white.withAlpha(60)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(32),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: child,
          ),
        ),
      ),
    );
  }
}