import 'dart:io';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/catalog_service.dart';
import 'brand_provider.dart';
import 'category_provider.dart';

/// --- Catalog State Class ---
class CatalogState {
  final AsyncValue<List<dynamic>> catalogs;

  const CatalogState({required this.catalogs});

  CatalogState copyWith({AsyncValue<List<dynamic>>? catalogs}) {
    return CatalogState(catalogs: catalogs ?? this.catalogs);
  }
}

/// --- State Notifier Class ---
class CatalogNotifier extends StateNotifier<CatalogState> {
  CatalogNotifier()
    : super(const CatalogState(catalogs: AsyncValue.loading())) {
    fetchCatalogs();
  }

  Future<void> fetchCatalogs() async {
    try {
      final data = await CatalogService.fetchCatalogs();
      state = state.copyWith(catalogs: AsyncValue.data(data.reversed.toList()));
    } catch (e, st) {
      print('Error fetching catalogs: $e');

      // Handle specific server errors more gracefully
      String errorMessage = 'Failed to load catalogs';
      if (e.toString().contains('500')) {
        errorMessage =
            'Server error: Unable to load catalogs. Please try again later.';
      } else if (e.toString().contains('401') || e.toString().contains('403')) {
        errorMessage = 'Access denied. Please login again.';
      } else if (e.toString().contains('network') ||
          e.toString().contains('connection')) {
        errorMessage = 'Network error. Please check your internet connection.';
      }

      final customError = Exception(errorMessage);
      state = state.copyWith(catalogs: AsyncValue.error(customError, st));
    }
  }

  Future<void> addCatalogAndRefresh({
    required String brandName,
    required String catalogNumber,
    required int categoryId,
    required int manufacturerId,
    required List<File> images,
    WidgetRef? ref,
  }) async {
    await CatalogService.addCatalog(
      brandName: brandName,
      catalogNumber: catalogNumber,
      categoryId: categoryId,
      manufacturerId: manufacturerId,
      images: images,
    );
    await fetchCatalogs();

    // Also refresh brand-specific catalog cache if ref is provided
    if (ref != null) {
      refreshBrandCatalogs(ref);

      // Refresh categories to update catalog counts
      ref.read(categoryProvider.notifier).fetchCategories();

      // Invalidate category catalogs provider for the specific category
      ref.invalidate(categoryCatalogsProvider(categoryId));
    }
  }

  /// Refresh brand-specific catalog cache for all brands
  /// This should be called after adding a new catalog to ensure
  /// brand pages show the updated catalog list
  static void refreshBrandCatalogs(WidgetRef ref) {
    // Invalidate all brand catalog providers to force refresh
    ref.invalidate(brandCatalogsProvider);
  }

  Future<void> uploadMoreImagesAndRefresh({
    required int catalogId,
    required String brandName,
    required String catalogNumber,
    required String manufacturerName,
    required List<File> images,
    WidgetRef? ref,
  }) async {
    try {
      await CatalogService.uploadMoreImages(
        catalogId: catalogId,
        brandName: brandName,
        catalogNumber: catalogNumber,
        manufacturerName: manufacturerName,
        images: images,
      );

      // Refresh the catalogs list
      await fetchCatalogs();

      // Also refresh brand-specific catalog cache if ref is provided
      if (ref != null) {
        refreshBrandCatalogs(ref);

        // Refresh categories to update catalog counts
        ref.read(categoryProvider.notifier).fetchCategories();
      }
    } catch (e) {
      print('Error in uploadMoreImagesAndRefresh: $e');
      rethrow; // Re-throw to propagate the error
    }
  }
}

/// --- Catalog List Provider ---
final catalogProvider = StateNotifierProvider<CatalogNotifier, CatalogState>(
  (ref) => CatalogNotifier(),
);

/// --- Catalog Details Provider (for specific catalog view) ---
final catalogDetailsProvider = StateProvider<AsyncValue<Map<String, dynamic>>>(
  (ref) => const AsyncValue.loading(),
);
final catalogNumberProvider = FutureProvider<String>((ref) async {
  return await CatalogService.generateCatalogNumber();
});

final fetchCatalogDetailsProvider = FutureProvider.family<
  Map<String, dynamic>,
  int
>((ref, id) async {
  try {
    final data = await CatalogService.fetchCatalogDetails(id);
    ref.read(catalogDetailsProvider.notifier).state = AsyncValue.data(data);
    return data;
  } catch (e, st) {
    // print('Error fetching catalog details for ID $id: $e');

    // Handle specific server errors more gracefully
    String errorMessage = 'Failed to load catalog details';
    if (e.toString().contains('500')) {
      errorMessage =
          'Server error: The catalog data may be corrupted. Please try again later.';
    } else if (e.toString().contains('404')) {
      errorMessage = 'Catalog not found';
    } else if (e.toString().contains('401') || e.toString().contains('403')) {
      errorMessage = 'Access denied. Please login again.';
    }

    final customError = Exception(errorMessage);
    ref.read(catalogDetailsProvider.notifier).state = AsyncValue.error(
      customError,
      st,
    );
    throw customError;
  }
});
final catalogCreateProvider = FutureProvider.family
    .autoDispose<Map<String, dynamic>, Map<String, dynamic>>((ref, data) async {
      return await CatalogService.addCatalog(
        brandName: data['brandName'],
        catalogNumber: data['catalogNumber'],
        categoryId: data['categoryId'],
        manufacturerId: data['manufacturerId'],
        images: data['images'],
      );
    });
