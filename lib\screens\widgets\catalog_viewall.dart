import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/screens/widgets/catalog_section.dart';
import 'package:mr_garments_mobile/services/catalog_service.dart';

class CatalogViewall extends StatefulWidget {
  const CatalogViewall({super.key});

  @override
  State<CatalogViewall> createState() => _CatalogViewallState();
}

class _CatalogViewallState extends State<CatalogViewall> {
  List<Map<String, String>> catalogList = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchCatalogs();
  }

  Future<void> _fetchCatalogs() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      final catalogs = await CatalogService.fetchCatalogs();
      catalogList = catalogs
          .map<Map<String, String>>((cat) => {
                'image': cat['image'] as String? ?? '',
                'brand': cat['brandName'] as String? ?? '',
                'catalog': cat['catalogNumber'] as String? ?? '',
              })
          .where((cat) => cat['image']!.isNotEmpty && cat['brand']!.isNotEmpty && cat['catalog']!.isNotEmpty)
          .toList();
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('All Catalogs'),
        foregroundColor: Colors.white,
        backgroundColor: const Color(0xFF005368),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(14),
                  child: CatalogSection(
                    catalogList: catalogList,
                    maxItems: null,
                  ),
                ),
    );
  }
}
