const functions = require('firebase-functions');
const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
admin.initializeApp();

/**
 * Cloud Function to send FCM notifications for chat messages
 * HTTP trigger - called from your Flutter app
 */
exports.sendChatNotification = functions.https.onRequest(async (req, res) => {
  // Set CORS headers
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    // Validate request body
    const { memberIds, chatId, senderName, messageText, messageType, senderId } = req.body;

    if (!memberIds || !Array.isArray(memberIds) || memberIds.length === 0) {
      res.status(400).json({ error: 'memberIds is required and must be a non-empty array' });
      return;
    }

    if (!chatId || !senderName || !messageText || !messageType || !senderId) {
      res.status(400).json({ 
        error: 'chatId, senderName, messageText, messageType, and senderId are required' 
      });
      return;
    }

    console.log('Sending notification for chat:', chatId, 'from:', senderName);
    console.log('Looking for FCM tokens for members:', memberIds);

    // Get FCM tokens for the member IDs
    const fcmTokens = await getFCMTokensForMembers(memberIds);

    if (fcmTokens.length === 0) {
      console.log('No FCM tokens found for members:', memberIds);
      res.status(404).json({ error: 'No FCM tokens found for the specified members' });
      return;
    }

    console.log(`Found ${fcmTokens.length} FCM tokens`);

    // Prepare notification content
    const notificationTitle = senderName;
    const notificationBody = messageType === 'image' ? '📷 Photo' : messageText;

    // Send notifications to all tokens
    const results = await Promise.allSettled(
      fcmTokens.map(token => sendNotificationToToken(
        token,
        notificationTitle,
        notificationBody,
        {
          chatId,
          otherUserName: senderName,
          isGroup: 'false',
          click_action: 'FLUTTER_NOTIFICATION_CLICK',
        }
      ))
    );

    // Process results
    const successful = results.filter(result => result.status === 'fulfilled').length;
    const failed = results.filter(result => result.status === 'rejected').length;

    console.log(`Notification results: ${successful} successful, ${failed} failed`);

    res.status(200).json({
      success: true,
      message: 'Notifications processed',
      results: {
        total: fcmTokens.length,
        successful,
        failed,
      }
    });

  } catch (error) {
    console.error('Error sending notifications:', error);
    res.status(500).json({ 
      error: 'Internal server error',
      message: error.message 
    });
  }
});

/**
 * Get FCM tokens for given member IDs from Firestore
 */
async function getFCMTokensForMembers(memberIds) {
  try {
    const tokens = [];
    const db = admin.firestore();

    console.log('Looking for FCM tokens for members:', memberIds);

    // Get FCM tokens from the users collection
    for (const memberId of memberIds) {
      try {
        console.log(`Getting FCM token for user: ${memberId}`);
        const userDoc = await db.collection('users').doc(memberId).get();
        if (userDoc.exists) {
          const userData = userDoc.data();
          console.log(`User ${memberId} exists, checking for FCM token...`);
          if (userData.fcmToken) {
            tokens.push(userData.fcmToken);
            console.log(`Found FCM token for user ${memberId}: ${userData.fcmToken.substring(0, 20)}...`);
          } else {
            console.log(`No FCM token found for user ${memberId}`);
          }
        } else {
          console.log(`User document not found for user ${memberId}`);
        }
      } catch (error) {
        console.error(`Error getting FCM token for user ${memberId}:`, error);
      }
    }

    console.log(`Total FCM tokens found: ${tokens.length}`);
    return tokens;
  } catch (error) {
    console.error('Error getting FCM tokens:', error);
    return [];
  }
}

/**
 * Send notification to a specific FCM token
 */
async function sendNotificationToToken(fcmToken, title, body, data) {
  try {
    const message = {
      token: fcmToken,
      notification: {
        title: title,
        body: body,
      },
      data: data,
      android: {
        notification: {
          sound: 'default',
          channelId: 'mr_garments_chat',
          priority: 'high',
        },
      },
      apns: {
        payload: {
          aps: {
            sound: 'default',
            badge: 1,
          },
        },
      },
    };

    const response = await admin.messaging().send(message);
    console.log('Successfully sent message to token:', fcmToken.substring(0, 20) + '...');
    return { success: true, messageId: response };

  } catch (error) {
    console.error('Error sending to token:', fcmToken.substring(0, 20) + '...', error);
    
    // Handle invalid tokens
    if (error.code === 'messaging/invalid-registration-token' || 
        error.code === 'messaging/registration-token-not-registered') {
      console.log('Invalid token detected, should be removed from database');
    }
    
    throw error;
  }
}
