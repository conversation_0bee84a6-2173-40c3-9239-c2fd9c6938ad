import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/providers/generic_retailer_provider.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class EditRetailerScreen extends ConsumerStatefulWidget {
  final Map<String, dynamic> retailer;

  const EditRetailerScreen({
    super.key,
    required this.retailer,
  });

  @override
  ConsumerState<EditRetailerScreen> createState() => _EditRetailerScreenState();
}

class _EditRetailerScreenState extends ConsumerState<EditRetailerScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _mobileController = TextEditingController();
  final _companyNameController = TextEditingController();
  final _addressController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _nameController.text = widget.retailer['name'] ?? '';
    // Try both possible field names for mobile
    _mobileController.text =
        widget.retailer['mobile'] ?? widget.retailer['mobile_number'] ?? '';
    _emailController.text = widget.retailer['email'] ?? '';
    _companyNameController.text = widget.retailer['company_name'] ?? '';
    _addressController.text = widget.retailer['address'] ?? '';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _mobileController.dispose();
    _companyNameController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  InputDecoration getDecoration(String label) => InputDecoration(
    labelText: label,
    filled: true,
    fillColor: Colors.grey.shade100,
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12),
      borderSide: BorderSide.none,
    ),
  );

  void _showSnack(String message) {
    AppSnackbar.showSuccess(context, message);
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) return;

    final retailerData = {
      "name": _nameController.text.trim(),
      "email": _emailController.text.trim(),
      "mobile_number": _mobileController.text.trim(),
      "company_name": _companyNameController.text.trim(),
      "address": _addressController.text.trim(),
      "status": "approved", // Keep status as approved
    };

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => const Center(child: CircularProgressIndicator()),
    );

    try {
      await ref.read(genericRetailerProvider.notifier).updateRetailer(
        widget.retailer['id'],
        retailerData,
        widget.retailer['distributor_id'] ?? 0,
      );

      if (!mounted) return;
      Navigator.pop(context);
      _showSnack('Retailer updated successfully');
      Navigator.pop(context);
    } catch (e) {
      Navigator.pop(context);
      AppSnackbar.showError(context, 'Error: ${e.toString()}');
    }
  }

  Future<void> _deactivate() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Deactivate Retailer',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
            content: Text(
              'Are you sure you want to deactivate this retailer?',
              style: GoogleFonts.poppins(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text(
                  'Cancel',
                  style: GoogleFonts.poppins(color: Colors.grey[600]),
                ),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: Text(
                  'Deactivate',
                  style: GoogleFonts.poppins(color: Colors.red),
                ),
              ),
            ],
          ),
    );

    if (confirmed != true) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => const Center(child: CircularProgressIndicator()),
    );

    try {
      await ref
          .read(genericRetailerProvider.notifier)
          .deactivateRetailer(widget.retailer['id'], widget.retailer['distributor_id'] ?? 0);

      if (!mounted) return;
      Navigator.pop(context);
      _showSnack('Retailer deactivated successfully');
      Navigator.pop(context);
    } catch (e) {
      if (mounted) {
        Navigator.pop(context);
        AppSnackbar.showError(context, 'Error: ${e.toString()}');
      }
    }
  }

  @override
  Widget build(BuildContext context) => Scaffold(
    backgroundColor: Colors.white,
    appBar: AppBar(
      backgroundColor: const Color(0xFF005368),
      foregroundColor: Colors.white,
      title: Text(
        'Edit Retailer',
        style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
      ),
    ),
    body: SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Retailer Information',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF005368),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Update retailer details',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            
            // Personal Information
            TextFormField(
              controller: _nameController,
              decoration: getDecoration('Full Name'),
              validator: (v) => v!.isEmpty ? 'Required' : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _emailController,
              decoration: getDecoration('Email Address'),
              keyboardType: TextInputType.emailAddress,
              validator: (v) => v!.isEmpty || !RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(v)
                  ? 'Enter valid email'
                  : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _mobileController,
              decoration: getDecoration('Mobile Number'),
              keyboardType: TextInputType.phone,
              validator: (v) => v!.isEmpty || !RegExp(r'^\d{10}$').hasMatch(v)
                  ? 'Enter valid 10-digit number'
                  : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _companyNameController,
              decoration: getDecoration('Company Name'),
              validator: (v) => v!.isEmpty ? 'Required' : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _addressController,
              decoration: getDecoration('Address'),
              maxLines: 3,
              validator: (v) => v!.isEmpty ? 'Required' : null,
            ),
            
            const SizedBox(height: 32),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _deactivate,
                    icon: const Icon(Icons.person_off, color: Colors.red),
                    label: Text(
                      'Deactivate Retailer',
                      style: GoogleFonts.poppins(color: Colors.red),
                    ),
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Colors.red),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _submit,
                    icon: const Icon(LucideIcons.save, color: Colors.white),
                    label: Text(
                      'Update Retailer',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF005368),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ),
  );
}
