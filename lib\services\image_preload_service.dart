import 'dart:async';
import 'dart:ui';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/utils/enhanced_image_cache.dart';

/// Service to handle intelligent image preloading for WhatsApp-like experience
class ImagePreloadService {
  static final ImagePreloadService _instance = ImagePreloadService._internal();
  factory ImagePreloadService() => _instance;
  ImagePreloadService._internal();

  // Track preloading status for different chats
  final Map<String, bool> _preloadingChats = {};
  final Map<String, DateTime> _lastPreloadTime = {};
  final Map<String, Set<String>> _preloadedImages = {};

  /// Preload images for a chat when user enters it
  Future<void> preloadChatImages({
    required String chatId,
    required List<Message> messages,
    Function(int loaded, int total)? onProgress,
    VoidCallback? onComplete,
  }) async {
    // Avoid duplicate preloading
    if (_preloadingChats[chatId] == true) {
      return;
    }

    // Check if we recently preloaded this chat (within 5 minutes)
    final lastPreload = _lastPreloadTime[chatId];
    if (lastPreload != null && 
        DateTime.now().difference(lastPreload).inMinutes < 5) {
      return;
    }

    _preloadingChats[chatId] = true;
    _lastPreloadTime[chatId] = DateTime.now();

    try {
      // Extract image URLs from messages
      final imageUrls = _extractImageUrls(messages);
      
      // Filter out already preloaded images
      final newImageUrls = imageUrls.where((url) {
        final preloadedSet = _preloadedImages[chatId] ?? {};
        return !preloadedSet.contains(url);
      }).toList();

      if (newImageUrls.isEmpty) {
        onComplete?.call();
        return;
      }

      // Preload images with priority (recent messages first)
      await _preloadWithPriority(
        chatId: chatId,
        imageUrls: newImageUrls,
        messages: messages,
        onProgress: onProgress,
        onComplete: onComplete,
      );

    } finally {
      _preloadingChats[chatId] = false;
    }
  }

  /// Extract image URLs from messages, prioritizing recent ones
  List<String> _extractImageUrls(List<Message> messages) {
    final imageUrls = <String>[];
    
    // Sort messages by timestamp (newest first) for priority preloading
    final sortedMessages = List<Message>.from(messages)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

    for (final message in sortedMessages) {
      if (message.type == MessageType.image && 
          message.mediaUrl != null && 
          message.mediaUrl!.isNotEmpty) {
        imageUrls.add(message.mediaUrl!);
      }
    }

    return imageUrls;
  }

  /// Preload images with priority (recent messages first)
  Future<void> _preloadWithPriority({
    required String chatId,
    required List<String> imageUrls,
    required List<Message> messages,
    Function(int loaded, int total)? onProgress,
    VoidCallback? onComplete,
  }) async {
    if (imageUrls.isEmpty) {
      onComplete?.call();
      return;
    }

    // Initialize preloaded set for this chat
    _preloadedImages.putIfAbsent(chatId, () => {});

    // Preload in batches for better performance
    const batchSize = 5;
    int totalLoaded = 0;
    final total = imageUrls.length;

    for (int i = 0; i < imageUrls.length; i += batchSize) {
      final batch = imageUrls.skip(i).take(batchSize).toList();
      
      // Preload batch concurrently
      final futures = batch.map((url) => _preloadSingleImage(chatId, url));
      await Future.wait(futures);
      
      totalLoaded += batch.length;
      onProgress?.call(totalLoaded, total);

      // Small delay between batches to avoid overwhelming the system
      if (i + batchSize < imageUrls.length) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
    }

    onComplete?.call();
  }

  /// Preload a single image and track it
  Future<void> _preloadSingleImage(String chatId, String imageUrl) async {
    try {
      final success = await EnhancedImageCache.preloadImage(imageUrl);
      if (success) {
        _preloadedImages[chatId]?.add(imageUrl);
      }
    } catch (e) {
      // Silently handle errors
      print('Failed to preload image: $imageUrl');
    }
  }

  /// Preload images for visible messages (when scrolling)
  Future<void> preloadVisibleImages({
    required String chatId,
    required List<Message> visibleMessages,
  }) async {
    final imageUrls = _extractImageUrls(visibleMessages);
    
    // Preload only new images
    final newImageUrls = imageUrls.where((url) {
      final preloadedSet = _preloadedImages[chatId] ?? {};
      return !preloadedSet.contains(url);
    }).toList();

    if (newImageUrls.isNotEmpty) {
      // Preload without progress tracking for visible images
      await _preloadWithPriority(
        chatId: chatId,
        imageUrls: newImageUrls,
        messages: visibleMessages,
      );
    }
  }

  /// Preload adjacent images when viewing an image
  Future<void> preloadAdjacentImages({
    required List<String> allImageUrls,
    required int currentIndex,
    int adjacentCount = 3,
  }) async {
    final imagesToPreload = <String>[];
    
    // Preload next images
    for (int i = 1; i <= adjacentCount; i++) {
      final nextIndex = currentIndex + i;
      if (nextIndex < allImageUrls.length) {
        imagesToPreload.add(allImageUrls[nextIndex]);
      }
    }
    
    // Preload previous images
    for (int i = 1; i <= adjacentCount; i++) {
      final prevIndex = currentIndex - i;
      if (prevIndex >= 0) {
        imagesToPreload.add(allImageUrls[prevIndex]);
      }
    }

    // Preload in background
    if (imagesToPreload.isNotEmpty) {
      EnhancedImageCache.preloadImages(imagesToPreload);
    }
  }

  /// Check if chat images are being preloaded
  bool isChatPreloading(String chatId) {
    return _preloadingChats[chatId] ?? false;
  }

  /// Get preloaded image count for a chat
  int getPreloadedImageCount(String chatId) {
    return _preloadedImages[chatId]?.length ?? 0;
  }

  /// Clear preload cache for a specific chat
  void clearChatPreloadCache(String chatId) {
    _preloadingChats.remove(chatId);
    _lastPreloadTime.remove(chatId);
    _preloadedImages.remove(chatId);
  }

  /// Clear all preload cache
  void clearAllPreloadCache() {
    _preloadingChats.clear();
    _lastPreloadTime.clear();
    _preloadedImages.clear();
  }

  /// Get preload statistics
  Map<String, dynamic> getPreloadStats() {
    return {
      'activePreloads': _preloadingChats.values.where((v) => v).length,
      'totalChatsTracked': _preloadedImages.length,
      'totalPreloadedImages': _preloadedImages.values
          .map((set) => set.length)
          .fold(0, (sum, count) => sum + count),
    };
  }
}