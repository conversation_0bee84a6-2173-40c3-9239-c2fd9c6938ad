import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/providers/manufacturer_provider.dart';
 
import 'package:mr_garments_mobile/screens/Auth/widgets/glassmorphic_card.dart';

class ManufacturerDetailsTab extends ConsumerWidget {
  final int manufacturerId;  
  const ManufacturerDetailsTab({super.key, required this.manufacturerId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final detailsAsync = ref.watch(manufacturerDetailsProvider(manufacturerId));
    return detailsAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, _) => Center(child: Text("Error: $e")),
      data: (details) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: GlassmorphicCard(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDetailItem(
                    "Company Name",
                    details['companyName'] ?? '-',
                  ),
                  _buildDetailItem(
                    "Contact Person Name",
                    details['contactPerson'] ?? '-',
                  ),
                  _buildDetailItem("Mobile No.", details['mobile'] ?? '-'),
                  _buildDetailItem("Email ID", details['email'] ?? '-'),
                  _buildDetailItem("Address", details['address'] ?? '-'),
                  _buildDetailItem("GST No.", details['gstNumber'] ?? '-'),
                  _buildDetailItem(
                    "Credit Limit",
                    "₹${details['creditLimit']}",
                  ),
                  _buildDetailItem(
                    "Brands",
                    (details['brands'] as List).join(", "),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: _labelStyle()),
          const SizedBox(height: 4),
          Text(value, style: _valueStyle()),
        ],
      ),
    );
  }

  TextStyle _labelStyle() => GoogleFonts.poppins(
    fontSize: 13,
    color: Colors.grey[700],
    fontWeight: FontWeight.w500,
  );

  TextStyle _valueStyle() => GoogleFonts.poppins(
    fontSize: 15,
    color: Colors.black,
    fontWeight: FontWeight.w600,
  );
}
