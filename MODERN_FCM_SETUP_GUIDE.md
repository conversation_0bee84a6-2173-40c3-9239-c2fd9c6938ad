# Firebase Cloud Functions Notification Setup Guide

This guide shows how to implement Firebase Cloud Messaging using Firebase Cloud Functions with the Firebase Admin SDK. No server key required!

## 🔑 Service Account Setup

You already have the service account JSON file: `mrgarments-f3b34-7687b72bb01d.json`

## 🚀 Firebase Cloud Functions Implementation

### Step 1: Initialize Firebase Functions

```bash
# Install Firebase CLI globally
npm install -g firebase-tools

# Login to Firebase
firebase login

# Navigate to your project directory
cd /path/to/your/project

# Initialize Firebase Functions
firebase init functions
```

When prompted:
- Select "Use an existing project" → `mrgarments-f3b34`
- Choose JavaScript or TypeScript (we'll use JavaScript)
- Install dependencies with npm → Yes

### Step 2: Install Dependencies

```bash
cd functions
npm install firebase-admin firebase-functions
```

### Step 3: Configure Functions

The Cloud Function code is already provided in `functions/index.js`:


### Step 4: Deploy Functions

```bash
# Deploy the functions
firebase deploy --only functions

# Note the function URLs from the output
```

### Step 5: Update Client Code

Update the backend URL in your Flutter app with your actual function URL:

```dart
// In lib/services/chat_service.dart
const backendUrl = 'https://us-central1-mrgarments-f3b34.cloudfunctions.net/sendChatNotification';
```

## 🔧 Client-Side Updates

The client-side code has already been updated to use Cloud Functions. No server key is needed in the Flutter app.

### Key Changes Made:

1. **Removed Legacy FCM Code**: No more direct FCM calls from the client
2. **Cloud Function Integration**: Notifications are now sent via Firebase Cloud Functions
3. **Secure Approach**: Service account credentials are handled automatically by Firebase

## 🧪 Testing

### Test Cloud Function

```bash
curl -X POST https://us-central1-mrgarments-f3b34.cloudfunctions.net/sendChatNotification \
  -H "Content-Type: application/json" \
  -d '{
    "memberIds": ["user1", "user2"],
    "chatId": "test_chat_123",
    "senderName": "Test User",
    "messageText": "Hello from Cloud Function!",
    "messageType": "text",
    "senderId": "sender_123"
  }'
```

### Test from Flutter App

```dart
import 'package:mr_garments_mobile/utils/notification_test_helper.dart';

// Test the complete notification flow
NotificationTestHelper.runComprehensiveTest();

// Test backend endpoint specifically
NotificationTestHelper.testBackendNotification();
```

## 🔒 Security Considerations

### 1. Service Account Security

- ✅ **DO**: Firebase handles service account automatically in Cloud Functions
- ❌ **DON'T**: Include service account in your mobile app
- ✅ **DO**: Use Firebase security rules for Firestore access
- ❌ **DON'T**: Commit sensitive data to version control

### 2. Function Security

```javascript
// Add authentication to your Cloud Function
exports.sendChatNotification = functions.https.onRequest(async (req, res) => {
  // Verify the request is from your app
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    res.status(401).json({ error: 'Unauthorized' });
    return;
  }

  // Your function logic here...
});
```

### 3. Rate Limiting

Firebase Cloud Functions have built-in rate limiting, but you can add custom limits:

```javascript
// Simple rate limiting example
const rateLimiter = new Map();

exports.sendChatNotification = functions.https.onRequest(async (req, res) => {
  const clientIP = req.ip;
  const now = Date.now();

  if (rateLimiter.has(clientIP)) {
    const lastRequest = rateLimiter.get(clientIP);
    if (now - lastRequest < 1000) { // 1 second cooldown
      res.status(429).json({ error: 'Rate limit exceeded' });
      return;
    }
  }

  rateLimiter.set(clientIP, now);
  // Your function logic here...
});
```

## 🚀 Production Deployment

### Cloud Functions Deployment

1. **Deploy to Production**

```bash
# Deploy all functions
firebase deploy --only functions

# Deploy specific function
firebase deploy --only functions:sendChatNotification

# Deploy with specific project
firebase use mrgarments-f3b34
firebase deploy --only functions
```

2. **Monitor Functions**

```bash
# View function logs
firebase functions:log

# View specific function logs
firebase functions:log --only sendChatNotification
```

3. **Set Environment Variables** (if needed)

```bash
# Set custom configuration
firebase functions:config:set someservice.key="THE API KEY"

# Get current config
firebase functions:config:get
```

## 📊 Monitoring

### Cloud Functions Monitoring

1. **View Logs**

```bash
# View all function logs
firebase functions:log

# View specific function logs
firebase functions:log --only sendChatNotification

# View logs in real-time
firebase functions:log --follow
```

2. **Firebase Console Monitoring**

- Go to [Firebase Console](https://console.firebase.google.com/)
- Select your project: `mrgarments-f3b34`
- Navigate to Functions → Logs
- Monitor function executions, errors, and performance

3. **Add Custom Logging**

```javascript
// In your Cloud Function
console.log('Notification sent successfully', {
  chatId: chatId,
  sender: senderName,
  recipients: fcmTokens.length,
  timestamp: new Date().toISOString()
});
```

## 🔄 Migration from Legacy

If you were using the old server key approach:

1. ✅ **Client code updated** - No more direct FCM calls
2. ✅ **Cloud Functions implemented** - Using Firebase Admin SDK
3. ✅ **Security improved** - No credentials in client app
4. ✅ **Modern API** - Using FCM v1 API
5. ✅ **Serverless** - No server maintenance required

## 📱 Client-Side Testing

Use the existing test helper:

```dart
import 'package:mr_garments_mobile/utils/notification_test_helper.dart';

// Test the complete flow
NotificationTestHelper.runComprehensiveTest();
```

## ✅ Checklist

- [ ] Firebase CLI installed and logged in
- [ ] Firebase Functions initialized for project
- [ ] Cloud Function code deployed (`sendChatNotification`)
- [ ] Client code updated to use Cloud Function URL
- [ ] FCM tokens are being stored in Firestore
- [ ] Function security configured (optional)
- [ ] Logging and monitoring set up
- [ ] Testing completed with real devices

## 🆘 Troubleshooting

### Common Issues

1. **"Invalid service account"**
   - Verify JSON file path and permissions
   - Check project ID matches

2. **"No FCM tokens found"**
   - Ensure FCM tokens are being saved to database
   - Check database query in `getFCMTokensForMembers`

3. **"Notification not received"**
   - Verify FCM token is valid and current
   - Check device notification permissions
   - Test with a simple notification first

### Debug Steps

1. **Check FCM Token Registration**
2. **Verify Backend Endpoint Response**
3. **Test with Postman/curl**
4. **Check Firebase Console Logs**
5. **Verify Device Permissions**
