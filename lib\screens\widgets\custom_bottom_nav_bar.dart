import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/screens/manufacturer/Manufacturer_homescreen/manufacturer_home.dart';
import 'package:mr_garments_mobile/screens/retailer/retailer_homescreen/retailer_home.dart';
import 'package:mr_garments_mobile/screens/distributor/distributor_homescreen/distributor_home.dart';
import 'package:mr_garments_mobile/screens/admin/admin_home_page.dart';
import 'package:mr_garments_mobile/screens/widgets/products_tab.dart';
import 'package:mr_garments_mobile/screens/chat/chat_main_page.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class CustomBottomNavBar extends StatefulWidget {
  final int currentIndex;

  const CustomBottomNavBar({super.key, required this.currentIndex});

  @override
  State<CustomBottomNavBar> createState() => _CustomBottomNavBarState();
}

class _CustomBottomNavBarState extends State<CustomBottomNavBar> {
  String? _userRole;
  List<BottomNavigationBarItem> _navItems = [];

  @override
  void initState() {
    super.initState();
    _loadUserRole();
  }

  Future<void> _loadUserRole() async {
    try {
      final userRole = await SessionService.getUserRole();
      setState(() {
        _userRole = userRole;
        _navItems = _buildNavItems();
      });
    } catch (e) {
      // Handle error, default to showing all items
      setState(() {
        _navItems = _buildNavItems();
      });
    }
  }

  List<BottomNavigationBarItem> _buildNavItems() {
    List<BottomNavigationBarItem> items = [
      const BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
      const BottomNavigationBarItem(
        icon: Icon(LucideIcons.edit),
        label: 'Order',
      ),
      const BottomNavigationBarItem(
        icon: Icon(LucideIcons.package),
        label: 'Product',
      ),
    ];

    // Only add Ledger tab if user is not staff or retailer
    if (_userRole != 'staff' && _userRole != 'retailer') {
      items.add(
        const BottomNavigationBarItem(
          icon: Icon(LucideIcons.dollarSign),
          label: 'Ledger',
        ),
      );
    }

    items.add(
      const BottomNavigationBarItem(
        icon: Icon(LucideIcons.messageSquare),
        label: 'Chat',
      ),
    );

    return items;
  }

  int _getAdjustedIndex(int displayIndex) {
    // If ledger is hidden and we're clicking on chat (last item)
    if (_userRole == 'staff' || _userRole == 'retailer') {
      if (displayIndex >= 3) {
        return displayIndex + 1; // Skip ledger index (3) and map to chat (4)
      }
    }
    return displayIndex;
  }

  void _handleNavigation(BuildContext context, int index) async {
    final adjustedIndex = _getAdjustedIndex(index);
    if (adjustedIndex == 0) {
      // Home → Navigate to appropriate home screen based on user role
      await _navigateToHome(context);
    } else if (adjustedIndex == 2) {
      // Products → Replace stack with ProductsTab
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (_) => const ProductsTab()),
        (route) => false,
      );
    } else if (adjustedIndex == 4) {
      // Chat → Navigate to Chat Main Page
      Navigator.push(
        context,
        MaterialPageRoute(builder: (_) => const ChatMainPage()),
      );
    } else {
      // Other tabs → Show a placeholder message or implement later
      AppSnackbar.showInfo(context, 'This feature is under development!');
    }
  }

  Future<void> _navigateToHome(BuildContext context) async {
    try {
      final userRole = await SessionService.getUserRole();
      Widget homeScreen;

      switch (userRole?.toLowerCase()) {
        case 'manufacturer':
          homeScreen = const ManufacturerHomeScreen();
          break;
        case 'retailer':
          homeScreen = const RetailerHomeScreen();
          break;
        case 'distributor':
          homeScreen = const DistributorHomeScreen();
          break;
        case 'admin':
          homeScreen = const AdminHomePage();
          break;
        case 'salesperson':
          // SalesPerson users navigate to admin home screen
          homeScreen = const AdminHomePage();
          break;
        case 'staff':
          // Staff members navigate to their company's home screen
          // We need to get the company type from user data
          final userData = await SessionService.getUserData();
          final companyType =
              userData?['company_type']?.toString().toLowerCase();
          switch (companyType) {
            case 'manufacturer':
              homeScreen = const ManufacturerHomeScreen();
              break;
            case 'retailer':
              homeScreen = const RetailerHomeScreen();
              break;
            case 'distributor':
              homeScreen = const DistributorHomeScreen();
              break;
            default:
              // Default to manufacturer if company type is unknown
              homeScreen = const ManufacturerHomeScreen();
          }
          break;
        default:
          // If role is unknown, default to manufacturer
          homeScreen = const ManufacturerHomeScreen();
      }

      if (mounted && context.mounted) {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (_) => homeScreen),
          (route) => false,
        );
      }
    } catch (e) {
      // If there's an error, default to manufacturer home
      if (mounted && context.mounted) {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (_) => const ManufacturerHomeScreen()),
          (route) => false,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // If navigation items are not loaded yet, show a loading state or default items
    if (_navItems.isEmpty) {
      return BottomNavigationBar(
        currentIndex: 0,
        selectedItemColor: const Color(0xFF00536B),
        unselectedItemColor: Colors.grey,
        onTap: (index) => {},
        showUnselectedLabels: true,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(LucideIcons.edit), label: 'Order'),
          BottomNavigationBarItem(
            icon: Icon(LucideIcons.package),
            label: 'Product',
          ),
          BottomNavigationBarItem(
            icon: Icon(LucideIcons.messageSquare),
            label: 'Chat',
          ),
        ],
      );
    }

    // Adjust current index for display when ledger is hidden
    int displayIndex = widget.currentIndex;
    if ((_userRole == 'staff' || _userRole == 'retailer') &&
        widget.currentIndex == 4) {
      displayIndex = 3; // Map chat index from 4 to 3 when ledger is hidden
    }

    return BottomNavigationBar(
      currentIndex: displayIndex,
      selectedItemColor: const Color(0xFF00536B),
      unselectedItemColor: Colors.grey,
      onTap: (index) => _handleNavigation(context, index),
      showUnselectedLabels: true,
      items: _navItems,
    );
  }
}
