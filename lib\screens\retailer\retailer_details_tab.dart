import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/providers/retailer_provider.dart';
import 'package:mr_garments_mobile/screens/Auth/widgets/glassmorphic_card.dart';
 
class RetailerDetailsTab extends ConsumerWidget {
   final int retailerId;
  const RetailerDetailsTab({super.key, required this.retailerId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final detailsState = ref.watch(retailersProvider).retailerDetails;

    return detailsState.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, _) => Center(child: Text("Error: $e")),
      data: (data) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: GlassmorphicCard(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailItem("Company Name", data['companyName'] ?? 'N/A',),
              _buildDetailItem("Contact Person Name",   data['contactPerson'] ?? 'N/A',),
              _buildDetailItem("Mobile No.", data['mobile'] ?? 'N/A'),
              _buildDetailItem("Email ID", data['email'] ?? 'N/A'),
              _buildDetailItem("Address", data['address'] ?? 'N/A'),
              _buildDetailItem("GST No.", data['gstNo'] ?? 'N/A'),
              _buildDetailItem("Credit Limit", "₹ ${data['creditLimit'] ?? 0}"),
              _buildDetailItem("Total Invoice", data['totalInvoices'].toString(),),
              _buildDetailItem("Pending Payments","₹ ${data['pendingPayments'] ?? 0}",),
            ],
          ),
        ),
      ),
    );
  }
    );
  }
 Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.w500,
              fontSize: 13,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 15,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}