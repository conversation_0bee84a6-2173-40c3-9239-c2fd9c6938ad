import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/screens/ledgers/view_ledger.dart';

class LedgerList extends StatelessWidget {
  final String searchQuery;
  final String ledgerType;

  const LedgerList({
    super.key,
    required this.searchQuery,
    required this.ledgerType,
  });

  // Dummy data for ledgers
  List<Map<String, dynamic>> get _dummyLedgers => [
    {
      'id': '1',
      'companyName': 'XYZ Garments',
      'type': 'Manufacturer',
      'paymentStatus': 'Pending',
      'invoiceNo': 'TT22-0048',
      'invoiceDate': '10/07/2022',
      'dueDate': '15/07/2022',
      'amount': '12,123,493',
      'contactPerson': '<PERSON>',
      'mobile': '9090909090',
      'email': '<EMAIL>',
      'address': 'Barabazar, Kolkata, India, 700001',
      'gstNo': '1234XYZ123M',
      'creditLimit': '12000000',
    },
    {
      'id': '2',
      'companyName': 'Fashion Reboot',
      'type': 'Retailer',
      'paymentStatus': 'Pending',
      'invoiceNo': 'TT22-0047',
      'invoiceDate': '08/07/2022',
      'dueDate': '15/07/2022',
      'amount': '50,000,000',
      'contactPerson': 'Priya Sharma',
      'mobile': '9876543210',
      'email': '<EMAIL>',
      'address': 'Park Street, Kolkata, India, 700016',
      'gstNo': '5678ABC567R',
      'creditLimit': '50000000',
    },
    {
      'id': '3',
      'companyName': 'ABC Fashion',
      'type': 'Distributor',
      'paymentStatus': 'Paid',
      'invoiceNo': 'TT22-0046',
      'invoiceDate': '07/07/2022',
      'dueDate': '10/07/2022',
      'amount': '1,000,000',
      'contactPerson': 'Amit Kumar',
      'mobile': '9123456789',
      'email': '<EMAIL>',
      'address': 'Salt Lake, Kolkata, India, 700064',
      'gstNo': '9012DEF901D',
      'creditLimit': '10000000',
    },
    {
      'id': '4',
      'companyName': 'Levis Lifestyle',
      'type': 'Manufacturer',
      'paymentStatus': 'Over Due',
      'invoiceNo': 'TT22-0045',
      'invoiceDate': '05/07/2022',
      'dueDate': '08/07/2022',
      'amount': '25,000,000',
      'contactPerson': 'Rohit Singh',
      'mobile': '9988776655',
      'email': '<EMAIL>',
      'address': 'New Town, Kolkata, India, 700156',
      'gstNo': '3456GHI345M',
      'creditLimit': '30000000',
    },
  ];

  List<Map<String, dynamic>> get _filteredLedgers {
    var filtered = _dummyLedgers;

    // Filter by ledger type
    if (ledgerType != 'All Ledger') {
      String typeFilter = ledgerType.replaceAll(' Ledger', '');
      filtered =
          filtered.where((ledger) => ledger['type'] == typeFilter).toList();
    }

    // Filter by search query
    if (searchQuery.isNotEmpty) {
      filtered =
          filtered.where((ledger) {
            return ledger['companyName'].toLowerCase().contains(
                  searchQuery.toLowerCase(),
                ) ||
                ledger['invoiceNo'].toLowerCase().contains(
                  searchQuery.toLowerCase(),
                );
          }).toList();
    }

    return filtered;
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'paid':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'over due':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildLedgerCard(BuildContext context, Map<String, dynamic> ledger) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 3,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.white, Colors.grey.shade50],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      ledger['companyName'],
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF00536B),
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(ledger['paymentStatus']),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      ledger['paymentStatus'],
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildInfoRow('Invoice No.', ledger['invoiceNo']),
                  ),
                  Expanded(
                    child: _buildInfoRow('Invoice Date', ledger['invoiceDate']),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(child: _buildInfoRow('Due Date', ledger['dueDate'])),
                  Expanded(
                    child: _buildInfoRow('Amount', '₹${ledger['amount']}'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ViewLedger(ledgerData: ledger),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF00536B),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 10,
                      ),
                    ),
                    child: Text(
                      'View',
                      style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 12,
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: const Color(0xFF00536B),
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final filteredLedgers = _filteredLedgers;

    if (filteredLedgers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt_long, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'No ledgers found',
              style: GoogleFonts.poppins(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search or filter',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: filteredLedgers.length,
      itemBuilder: (context, index) {
        return _buildLedgerCard(context, filteredLedgers[index]);
      },
    );
  }
}
