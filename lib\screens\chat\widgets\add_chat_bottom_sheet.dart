import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/models/chat_user.dart';
import 'package:mr_garments_mobile/providers/chat_provider.dart';
import 'package:mr_garments_mobile/screens/chat/member_chat_inbox.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class AddChatBottomSheet extends ConsumerStatefulWidget {
  const AddChatBottomSheet({super.key});

  @override
  ConsumerState<AddChatBottomSheet> createState() => _AddChatBottomSheetState();
}

class _AddChatBottomSheetState extends ConsumerState<AddChatBottomSheet> {
  final TextEditingController _searchController = TextEditingController();
  List<ChatUser> _searchResults = [];
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _loadAllUsers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadAllUsers() async {
    final users = await ref.read(allUsersProvider.future);
    setState(() {
      _searchResults = users;
    });
  }

  void _searchUsers(String query) async {
    if (query.isEmpty) {
      _loadAllUsers();
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      final chatNotifier = ref.read(chatProvider.notifier);
      final results = await chatNotifier.searchUsers(query);
      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _isSearching = false;
      });
      if (mounted) {
        AppSnackbar.showError(context, 'Error searching users: $e');
      }
    }
  }

  void _startChat(ChatUser user) async {
    // Store the navigator context before async operations
    final navigator = Navigator.of(context, rootNavigator: true);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Close bottom sheet first
    Navigator.of(context).pop();

    try {
      // print('Starting chat with user: ${user.name} (${user.id})');
      final chatNotifier = ref.read(chatProvider.notifier);
      final chatId = await chatNotifier.createIndividualChat(user.id);

      // print('Chat created with ID: $chatId');

      if (chatId != null) {
        // print('Navigating to chat screen...');
        navigator.push(
          MaterialPageRoute(
            builder:
                (context) => MemberChatInbox(
                  chatId: chatId,
                  chatName: user.name,
                  isGroup: false,
                ),
          ),
        );
        // print('Navigation completed');
      } else {
        // print('Chat ID is null, cannot navigate');
        // Check if there's an error in the provider
        ref.read(chatProvider);
        // print('Chat provider error: ${chatState.error}');

        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Failed to create chat'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      // print('Error creating chat: $e');
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Error creating chat: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  // void _createGroup() {
  //   Navigator.pop(context); // Close bottom sheet
  //   Navigator.push(
  //     context,
  //     MaterialPageRoute(builder: (context) => const CreateGroupPage()),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Start New Chat',
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF005368),
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close, color: Color(0xFF005368)),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // Create Group Button
          // Padding(
          //   padding: const EdgeInsets.symmetric(horizontal: 20),
          //   child: InkWell(
          //     onTap: _createGroup,
          //     borderRadius: BorderRadius.circular(12),
          //     child: Container(
          //       padding: const EdgeInsets.all(16),
          //       decoration: BoxDecoration(
          //         color: const Color(0xFF005368).withOpacity(0.1),
          //         borderRadius: BorderRadius.circular(12),
          //         border: Border.all(
          //           color: const Color(0xFF005368).withOpacity(0.2),
          //         ),
          //       ),
                // child: Row(
                //   children: [
                    // Container(
                    //   padding: const EdgeInsets.all(8),
                    //   decoration: const BoxDecoration(
                    //     color: Color(0xFF005368),
                    //     shape: BoxShape.circle,
                    //   ),
                    //   child: const Icon(
                    //     LucideIcons.users,
                    //     color: Colors.white,
                    //     size: 20,
                    //   ),
                    // ),
                    // const SizedBox(width: 12),
                    // Expanded(
                    //   child: Column(
                    //     crossAxisAlignment: CrossAxisAlignment.start,
                    //     children: [
                    //       Text(
                    //         'Create Group',
                    //         style: GoogleFonts.poppins(
                    //           fontSize: 16,
                    //           fontWeight: FontWeight.w600,
                    //           color: const Color(0xFF005368),
                    //         ),
                    //       ),
                    //       Text(
                    //         'Start a group conversation',
                    //         style: GoogleFonts.poppins(
                    //           fontSize: 14,
                    //           color: Colors.grey[600],
                    //         ),
                    //       ),
                    //     ],
                    //   ),
                    // ),
                    // const Icon(
                    //   LucideIcons.chevronRight,
                    //   color: Color(0xFF005368),
                    //   size: 20,
                    // ),
                  // ],
          //       // ),
          //     ),
          //   ),
          // ),
          // const SizedBox(height: 20),
          // Search bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: TextField(
              controller: _searchController,
              onChanged: _searchUsers,
              decoration: InputDecoration(
                hintText: 'Search users...',
                hintStyle: GoogleFonts.poppins(color: Colors.grey[500]),
                prefixIcon: const Icon(
                  LucideIcons.search,
                  color: Color(0xFF005368),
                  size: 20,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF005368)),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Users list
          Expanded(
            child:
                _isSearching
                    ? const Center(
                      child: CircularProgressIndicator(
                        color: Color(0xFF005368),
                      ),
                    )
                    : _searchResults.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      itemCount: _searchResults.length,
                      itemBuilder: (context, index) {
                        final user = _searchResults[index];
                        return _buildUserItem(user);
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            LucideIcons.users,
            size: 64,
            color: Colors.grey.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No users found',
            style: GoogleFonts.poppins(fontSize: 16, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildUserItem(ChatUser user) {
    return InkWell(
      onTap: () => _startChat(user),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        child: Row(
          children: [
            // Profile picture
            CircleAvatar(
              radius: 24,
              backgroundColor: const Color(0xFF005368).withOpacity(0.1),
              backgroundImage:
                  user.profileImageUrl != null &&
                          user.profileImageUrl!.isNotEmpty
                      ? NetworkImage(user.profileImageUrl!)
                      : null,
              child:
                  user.profileImageUrl == null || user.profileImageUrl!.isEmpty
                      ? const Icon(
                        LucideIcons.user,
                        color: Color(0xFF005368),
                        size: 24,
                      )
                      : null,
            ),
            const SizedBox(width: 12),
            // User details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.name,
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF005368),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    user.email,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  if (user.role.isNotEmpty)
                    Text(
                      user.role.toUpperCase(),
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFFF2A738),
                      ),
                    ),
                ],
              ),
            ),
            // Online indicator
            if (user.isOnline)
              Container(
                width: 12,
                height: 12,
                decoration: const BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
