import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/services/local_storage_service.dart';
import 'package:mr_garments_mobile/utils/cache_manager.dart';

/// Smart image display utility for WhatsApp-like image handling
/// Automatically chooses between local and remote images
class SmartImageDisplay {
  /// Create an image widget that intelligently chooses between local and remote sources
  static Widget buildSmartImage({
    required Message message,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    VoidCallback? onTap,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    Widget imageWidget;

    // Determine the best image source
    if (message.hasLocalImage) {
      imageWidget = _buildLocalImage(
        localPath: message.localImagePath!,
        width: width,
        height: height,
        fit: fit,
        fallbackUrl: message.mediaUrl,
        errorWidget: errorWidget,
      );
    } else if (message.mediaUrl != null && message.mediaUrl!.isNotEmpty) {
      imageWidget = _buildRemoteImage(
        url: message.mediaUrl!,
        width: width,
        height: height,
        fit: fit,
        placeholder: placeholder,
        errorWidget: errorWidget,
      );
    } else {
      imageWidget = errorWidget ?? _buildDefaultErrorWidget(width, height);
    }

    // Apply border radius if specified
    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius,
        child: imageWidget,
      );
    }

    // Add tap gesture if specified
    if (onTap != null) {
      imageWidget = GestureDetector(
        onTap: onTap,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// Build thumbnail image widget
  static Widget buildSmartThumbnail({
    required Message message,
    double size = 60,
    BorderRadius? borderRadius,
    VoidCallback? onTap,
  }) {
    return buildSmartImage(
      message: message,
      width: size,
      height: size,
      fit: BoxFit.cover,
      borderRadius: borderRadius ?? BorderRadius.circular(8),
      onTap: onTap,
      placeholder: _buildThumbnailPlaceholder(size),
      errorWidget: _buildThumbnailError(size),
    );
  }

  /// Build local image widget
  static Widget _buildLocalImage({
    required String localPath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    String? fallbackUrl,
    Widget? errorWidget,
  }) {
    return Image.file(
      File(localPath),
      width: width,
      height: height,
      fit: fit,
      errorBuilder: (context, error, stackTrace) {
        // If local image fails and we have a fallback URL, try remote
        if (fallbackUrl != null && fallbackUrl.isNotEmpty) {
          return _buildRemoteImage(
            url: fallbackUrl,
            width: width,
            height: height,
            fit: fit,
            errorWidget: errorWidget,
          );
        }
        return errorWidget ?? _buildDefaultErrorWidget(width, height);
      },
    );
  }

  /// Build remote image widget with caching
  static Widget _buildRemoteImage({
    required String url,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return CachedNetworkImage(
      imageUrl: url,
      cacheManager: CustomCacheManager.chatInstance,
      width: width,
      height: height,
      fit: fit,
      placeholder: (context, url) => 
          placeholder ?? _buildDefaultPlaceholder(width, height),
      errorWidget: (context, url, error) => 
          errorWidget ?? _buildDefaultErrorWidget(width, height),
      fadeInDuration: const Duration(milliseconds: 200),
    );
  }

  /// Build default placeholder widget
  static Widget _buildDefaultPlaceholder(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
        ),
      ),
    );
  }

  /// Build default error widget
  static Widget _buildDefaultErrorWidget(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.broken_image,
              color: Colors.grey,
              size: 24,
            ),
            SizedBox(height: 4),
            Text(
              'Image not available',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 10,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build thumbnail placeholder
  static Widget _buildThumbnailPlaceholder(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 1.5,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
        ),
      ),
    );
  }

  /// Build thumbnail error widget
  static Widget _buildThumbnailError(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Icon(
          Icons.broken_image,
          color: Colors.grey,
          size: 20,
        ),
      ),
    );
  }

  /// Check if image should be loaded from local storage
  static Future<bool> shouldUseLocalImage(Message message) async {
    if (!message.hasLocalImage) return false;
    return await LocalStorageService.localFileExists(message.localImagePath);
  }

  /// Get the best available image path for a message
  static Future<String?> getBestImagePath(Message message) async {
    // Check local image first
    if (message.hasLocalImage) {
      final exists = await LocalStorageService.localFileExists(message.localImagePath);
      if (exists) {
        return message.localImagePath;
      }
    }
    
    // Fallback to remote URL
    return message.mediaUrl;
  }

  /// Get the best available thumbnail path for a message
  static Future<String?> getBestThumbnailPath(Message message) async {
    // Check local thumbnail first
    if (message.hasLocalThumbnail) {
      final exists = await LocalStorageService.localFileExists(message.localThumbnailPath);
      if (exists) {
        return message.localThumbnailPath;
      }
    }
    
    // Fallback to remote thumbnail or main image
    return message.thumbnailUrl ?? message.mediaUrl;
  }

  /// Preload image for better performance
  static Future<void> preloadImage(BuildContext context, Message message) async {
    try {
      final imagePath = await getBestImagePath(message);
      if (imagePath == null) return;

      if (message.hasLocalImage) {
        // Preload local image
        final image = FileImage(File(imagePath));
        await precacheImage(image, context);
      } else {
        // Preload remote image
        final image = CachedNetworkImageProvider(
          imagePath,
          cacheManager: CustomCacheManager.chatInstance,
        );
        await precacheImage(image, context);
      }
    } catch (e) {
      // Silently handle preload errors
      debugPrint('Error preloading image: $e');
    }
  }

  /// Batch preload multiple images
  static Future<void> preloadImages(
    BuildContext context, 
    List<Message> messages,
  ) async {
    final futures = messages
        .where((message) => message.type == MessageType.image)
        .map((message) => preloadImage(context, message));
    
    await Future.wait(futures);
  }
}
