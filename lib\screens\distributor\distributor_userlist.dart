import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/providers/distributor_provider.dart';
import 'package:mr_garments_mobile/screens/distributor/add_edit_distributor.dart';
import 'package:mr_garments_mobile/screens/distributor/distributor_details.dart';

class DistributorUserlist extends ConsumerWidget {
  final String searchQuery;
  const DistributorUserlist({super.key, required this.searchQuery});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final distributorState = ref.watch(distributorsProvider);

    return distributorState.distributors.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, _) => Center(child: Text("Error: $e")),
      data: (distributors) {
        final activeDistributors =
            distributors
                .where((m) => m['status']?.toLowerCase() != 'deactivated')
                .toList();
        // apply search filter here
         final filteredDistributors =
            activeDistributors.where((m) {
              final name = (m['name'] ?? '').toLowerCase();
              return name.contains(searchQuery.toLowerCase());
            }).toList();
        if (filteredDistributors.isEmpty) {
          return const Center(child: Text('No distributors found'));
        }
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount:
              filteredDistributors.length, // Replace with actual distributor count
          itemBuilder: (context, index) {
            final distributor = filteredDistributors[index];
            return GestureDetector(
              onTap: () {
                // Navigate to distributor details
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => DistributorDetailsScreen(
                          distributorId: int.parse(
                            distributor['id'].toString(),
                          ),
                        ),
                  ),
                );
              },
              child: Card(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                elevation: 6,
                shadowColor: Colors.black26,
                margin: const EdgeInsets.only(bottom: 16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const CircleAvatar(
                            radius: 18,
                            backgroundColor: Color(0xFF005368),
                            child: Icon(Icons.person, color: Colors.white),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  distributor['name'] ?? '',
                                  style: GoogleFonts.poppins(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                  ),
                                ),
                                Text(
                                  distributor['email'] ?? '',
                                  style: GoogleFonts.poppins(
                                    fontSize: 13,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                            onPressed: () {
                              // Edit Distributor
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) => AddEditDistributor(
                                        distributorId: int.parse(
                                          distributor['id'].toString(),
                                        ),
                                      ),
                                ),
                              );
                            },
                            icon: const Icon(
                              Icons.edit,
                              color: Color(0xFFF2A738),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          const Icon(
                            Icons.location_on,
                            size: 18,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Text(
                              distributor['address'] ?? '',
                              style: GoogleFonts.poppins(
                                fontSize: 13,
                                color: Colors.grey[800],
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      Row(
                        children: [
                          const Icon(
                            Icons.phone_android,
                            size: 18,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            distributor['mobile'] ?? '',
                            style: GoogleFonts.poppins(
                              fontSize: 13,
                              color: Colors.grey[800],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _infoBox(
                            label: "Catalog Count",
                            value: distributor['catalogCount'].toString(),
                          ),
                          _infoBox(
                            label: "Credit Limit",
                            value: "₹${distributor['creditLimit']}",
                          ),
                        ],
                      ),
                    ],
                  ),
                ), 
              ),
            );
          },
        );
      },
    ); 
  }

  Widget _infoBox({required String label, required String value}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: const Color.fromARGB(128, 225, 239, 247),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(fontSize: 12, color: Colors.black54),
          ),
          const SizedBox(height: 4),
          Text(value, style: GoogleFonts.poppins(fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }
}
