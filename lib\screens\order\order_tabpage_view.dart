import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/screens/admin/admin_home_page.dart';
import 'package:mr_garments_mobile/screens/order/create_order.dart';
import 'package:mr_garments_mobile/screens/order/order_list.dart';
// import 'package:mr_garments_mobile/screens/orders/create_order.dart';

class OrderTabPageView extends StatefulWidget {
  const OrderTabPageView({super.key});

  @override
  State<OrderTabPageView> createState() => _OrderTabPageViewState();
}

class _OrderTabPageViewState extends State<OrderTabPageView> {
  String selectedFilter = 'All';

  // Keep dummy orders list here to calculate counts
  final List<Map<String, dynamic>> orders = [
    {
      'orderId': 'ODR1234567',
      'status': 'Pending',
      'customer': 'XYZM Garments',
      'date': '10/07/2022',
    },
    {
      'orderId': 'ODR1234789',
      'status': 'Completed',
      'customer': 'XYZM Garments',
      'date': '10/07/2022',
    },
    {
      'orderId': 'ODR1234455',
      'status': 'Pending',
      'customer': 'XYZM Garments',
      'date': '10/07/2022',
    },
  ];

  int get newCount => orders.where((o) => o['status'] == 'Pending').length;
  int get completedCount =>
      orders.where((o) => o['status'] == 'Completed').length;

  Widget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xFF005368),
      foregroundColor: Colors.white,
      elevation: 0,
      title: Row(
        children: [
          Text(
            "Order List",
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () {
              Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(builder: (_) => const AdminHomePage()),
                (route) => false,
              );
            },
            child: const Icon(Icons.home, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterButtons() {
    Widget buildButton(String label, int count) {
      final bool isSelected = selectedFilter == label;
      return Expanded(
        child: GestureDetector(
          onTap: () {
            setState(() {
              selectedFilter = label;
            });
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 3),
            decoration: BoxDecoration(
              color: isSelected ? const Color(0xFF005368) : Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  label,
                  style: GoogleFonts.poppins(
                    color: isSelected ? Colors.white : Colors.black,
                    fontSize: 12.5,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 6),
                Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: 2,
                    horizontal: 6,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.white : Colors.grey,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '$count',
                    style: GoogleFonts.poppins(
                      color: isSelected ? Colors.black : Colors.white,
                      fontSize: 9,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.all(16),
      child: Row(
        children: [
          buildButton('All', orders.length),
          buildButton('New', newCount),
          buildButton('Completed', completedCount),
        ],
      ),
    );
  }

  Widget _buildCreateOrderButton() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      child: ElevatedButton.icon(
        icon: const Icon(Icons.add, color: Colors.white),
        label: Text(
          "Create Order",
          style: GoogleFonts.poppins(fontSize: 16, color: Colors.white),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFF2A738),
          padding: const EdgeInsets.symmetric(vertical: 14),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (_) => const CreateOrderScreen()),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(65),
        child: _buildAppBar(),
      ),
      body: Column(
        children: [
          _buildFilterButtons(),
          Expanded(child: OrderList(filterType: selectedFilter, orders: orders)),
          _buildCreateOrderButton(),
        ],
      ),
    );
  }
}
