import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/providers/generic_staff_provider.dart';
import 'package:mr_garments_mobile/providers/user_provider.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class EditStaff extends ConsumerStatefulWidget {
  final Map<String, dynamic> staff;

  const EditStaff({super.key, required this.staff});

  @override
  ConsumerState<EditStaff> createState() => _EditStaffState();
}

class _EditStaffState extends ConsumerState<EditStaff> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _mobileController = TextEditingController();
  final _emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _nameController.text = widget.staff['name'] ?? '';
    // Try both possible field names for mobile
    _mobileController.text =
        widget.staff['mobile'] ?? widget.staff['mobile_number'] ?? '';
    _emailController.text = widget.staff['email'] ?? '';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _mobileController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  InputDecoration getDecoration(String label) => InputDecoration(
    labelText: label,
    filled: true,
    fillColor: Colors.grey.shade100,
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12),
      borderSide: BorderSide.none,
    ),
  );

  void _showSnack(String message) {
    AppSnackbar.showSuccess(context, message);
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) return;

    final staffData = {
      "name": _nameController.text.trim(),
      "email": _emailController.text.trim(),
      "mobile_number": _mobileController.text.trim(),
      "status": "approved", // Keep status as approved
    };

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => const Center(child: CircularProgressIndicator()),
    );

    try {
      await ref
          .read(genericStaffProvider.notifier)
          .updateStaff(
            widget.staff['id'],
            staffData,
            widget.staff['company_id'] ?? 0,
          );

      // Also refresh the users list so changes appear in Users tab
      await ref.read(usersProvider.notifier).fetchUsers();

      if (!mounted) return;
      Navigator.pop(context);
      _showSnack('Staff updated successfully');
      Navigator.pop(context);
    } catch (e) {
      Navigator.pop(context);
      AppSnackbar.showError(context, 'Error: ${e.toString()}');
    }
  }

  Future<void> _deactivate() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Deactivate Staff',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
            content: Text(
              'Are you sure you want to deactivate this staff member?',
              style: GoogleFonts.poppins(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text(
                  'Cancel',
                  style: GoogleFonts.poppins(color: Colors.grey[600]),
                ),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: Text(
                  'Deactivate',
                  style: GoogleFonts.poppins(color: Colors.red),
                ),
              ),
            ],
          ),
    );

    if (confirmed != true) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => const Center(child: CircularProgressIndicator()),
    );

    try {
      await ref
          .read(genericStaffProvider.notifier)
          .deactivateStaff(widget.staff['id'], widget.staff['company_id'] ?? 0);

      // Also refresh the users list
      await ref.read(usersProvider.notifier).fetchUsers();

      if (!mounted) return;
      Navigator.pop(context);
      _showSnack('Staff deactivated successfully');
      Navigator.pop(context);
    } catch (e) {
      Navigator.pop(context);
      AppSnackbar.showError(context, 'Error: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) => Scaffold(
    backgroundColor: Colors.white,
    appBar: AppBar(
      backgroundColor: const Color(0xFF005368),
      foregroundColor: Colors.white,
      title: Text(
        'Edit Staff',
        style: GoogleFonts.poppins(color: Colors.white, fontSize: 18),
      ),
    ),
    body: Padding(
      padding: const EdgeInsets.all(20),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 20),
                    TextFormField(
                      controller: _nameController,
                      decoration: getDecoration('Full Name'),
                      validator:
                          (value) =>
                              value?.isEmpty == true
                                  ? 'Name is required'
                                  : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _mobileController,
                      decoration: getDecoration('Mobile Number'),
                      keyboardType: TextInputType.phone,
                      validator:
                          (value) =>
                              value?.isEmpty == true
                                  ? 'Mobile number is required'
                                  : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _emailController,
                      decoration: getDecoration('Email ID'),
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) {
                        if (value?.isEmpty == true) {
                          return 'Email is required';
                        }
                        if (!RegExp(
                          r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                        ).hasMatch(value!)) {
                          return 'Enter a valid email';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 24),
                    // Display role as read-only
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 18,
                        vertical: 16,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: const Color(0xFFE0E0E0)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Job Role',
                            style: GoogleFonts.poppins(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Staff',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              color: Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _deactivate,
                    icon: const Icon(Icons.person_off, color: Colors.red),
                    label: Text(
                      'Deactivate Staff',
                      style: GoogleFonts.poppins(color: Colors.red),
                    ),
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Colors.red),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _submit,
                    icon: const Icon(Icons.check, color: Colors.white),
                    label: Text(
                      'Update Staff',
                      style: GoogleFonts.poppins(color: Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFF2A738),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ),
  );
}
