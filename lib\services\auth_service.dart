// lib/services/auth_service.dart
import 'dart:convert';
import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/services/credential_service.dart';
import 'package:mr_garments_mobile/services/notification_service.dart';

class AuthService {
  static const String baseUrl = 'https://mrgarment.braincavesoft.com/api';
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Initialize Firebase Auth for chat features
  static Future<void> _initializeFirebaseAuth() async {
    try {
      // Sign in anonymously if not already authenticated
      if (_auth.currentUser == null) {
        await _auth.signInAnonymously();
      }
    } catch (e) {
      // Firebase auth initialization failed, but don't block login
    }
  }

  static Future<Map<String, dynamic>> register({
    required String name,
    required String email,
    required String mobile,
    required String password,
    required String accountType,
    required bool agreeTerms,
  }) async {
    final url = Uri.parse('$baseUrl/register');

    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'name': name,
          'email': email,
          'mobile_number': mobile,
          'password': password,
          'account_type': accountType,
          'agree_terms': agreeTerms,
        }),
      );
      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          "success": true,
          "message": responseData['message'],
          "token": responseData['token'],
          "user": responseData['user'],
        };
      } else {
        return {
          "success": false,
          "message": responseData['message'] ?? 'Registration failed',
        };
      }
    } on SocketException {
      return {"success": false, "message": 'Please connect to internet.'};
    } catch (e) {
      return {"success": false, "message": 'The email has already been taken.'};
    }
  }

  static Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    final url = Uri.parse('$baseUrl/login');

    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'email': email, 'password': password}),
      );
      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          "success": true,
          "message": responseData['message'],
          "token": responseData['token'],
          "user": responseData['user'],
        };
      } else if (response.statusCode == 401 || response.statusCode == 403) {
        return {"success": false, "message": "Invalid email or password."};
      } else {
        return {
          "success": false,
          "message":
              responseData['message'] ?? 'Login failed. Please try again.',
        };
      }
    } on SocketException {
      return {"success": false, "message": 'Please connect to internet.'};
    } catch (e) {
      return {"success": false, "message": 'Invalid email or password.'};
    }
  }

  static Future<Map<String, dynamic>> forgotPasswordRequest(
    String email,
  ) async {
    final url = Uri.parse('$baseUrl/forgot-password/request');

    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'email': email}),
    );

    try {
      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {"success": true, "message": responseData['message']};
      } else {
        return {
          "success": false,
          "message": responseData['message'] ?? 'Request failed',
        };
      }
    } catch (e) {
      return {"success": false, "message": 'Unexpected server response.'};
    }
  }

  static Future<Map<String, dynamic>> verifyOtp(
    String email,
    String otp,
  ) async {
    final url = Uri.parse('$baseUrl/forgot-password/verify');

    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'email': email, 'otp': otp}),
    );

    try {
      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {"success": true, "message": responseData['message']};
      } else {
        return {
          "success": false,
          "message": responseData['message'] ?? 'Verification failed',
        };
      }
    } catch (e) {
      return {"success": false, "message": 'Unexpected server response.'};
    }
  }

  static Future<Map<String, dynamic>> resetPassword({
    required String email,
    required String otp,
    required String password,
    required String passwordConfirmation,
  }) async {
    final url = Uri.parse('$baseUrl/forgot-password/reset');

    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'email': email,
        'otp': otp,
        'password': password,
        'password_confirmation': passwordConfirmation,
      }),
    );

    try {
      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {"success": true, "message": responseData['message']};
      } else {
        return {
          "success": false,
          "message": responseData['message'] ?? 'Reset password failed',
        };
      }
    } catch (e) {
      return {"success": false, "message": 'Unexpected server response.'};
    }
  }

  static Future<void> logout(String token) async {
    final url = Uri.parse('$baseUrl/logout');
    try {
      final response = await http.post(
        url,
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );
      if (response.statusCode == 200) {
        // Success - logout API responded with 200
        // Clear local session data
        await SessionService.clearSession();
      } else {
        throw Exception('Failed to logout. Status: ${response.statusCode}');
      }
    } on SocketException {
      throw Exception('Please connect to internet.');
    } catch (e) {
      throw Exception('Failed to logout. Error: $e');
    }
  }

  /// Enhanced login method that saves session data
  static Future<Map<String, dynamic>> loginWithSession({
    required String email,
    required String password,
  }) async {
    final result = await login(email: email, password: password);

    if (result['success'] == true &&
        result['token'] != null &&
        result['user'] != null) {
      // Save session data
      await SessionService.saveUserSession(
        token: result['token'],
        userData: result['user'],
      );

      // Initialize Firebase Auth for chat features
      await _initializeFirebaseAuth();

      // Sync user with Firebase for chat functionality
      await ChatService.syncCurrentUserWithFirebase();

      // Initialize notifications and save FCM token
      await _initializeNotificationsForUser();
    }

    return result;
  }

  /// Enhanced register method that saves session data
  static Future<Map<String, dynamic>> registerWithSession({
    required String name,
    required String email,
    required String mobile,
    required String password,
    required String accountType,
    required bool agreeTerms,
  }) async {
    final result = await register(
      name: name,
      email: email,
      mobile: mobile,
      password: password,
      accountType: accountType,
      agreeTerms: agreeTerms,
    );

    if (result['success'] == true &&
        result['token'] != null &&
        result['user'] != null) {
      // Save session data
      await SessionService.saveUserSession(
        token: result['token'],
        userData: result['user'],
      );

      // Initialize Firebase Auth for chat features
      await _initializeFirebaseAuth();

      // Sync user with Firebase for chat functionality
      await ChatService.syncCurrentUserWithFirebase();

      // Initialize notifications and save FCM token
      await _initializeNotificationsForUser();
    }

    return result;
  }

  /// Logout and clear session
  static Future<void> logoutWithSession() async {
    final token = await SessionService.getAuthToken();
    if (token != null) {
      try {
        await logout(token);
      } catch (e) {
        // Even if API call fails, clear local session
        await SessionService.clearSession();
        // Also clear saved credentials on logout
        await CredentialService.clearCredentials();
        rethrow;
      }
    } else {
      // No token found, just clear local session
      await SessionService.clearSession();
      // Also clear saved credentials on logout
      await CredentialService.clearCredentials();
    }
  }

  /// Initialize notifications and save FCM token for the current user
  static Future<void> _initializeNotificationsForUser() async {
    try {
      // Initialize notification service
      await NotificationService.initialize();

      // Get current user ID and FCM token
      final userId = await SessionService.getUserId();
      final token = await FirebaseMessaging.instance.getToken();

      if (userId != null && token != null) {
        // Save FCM token to Firestore
        await ChatService.updateFCMToken(userId.toString(), token);
        debugPrint('✅ FCM token saved for user $userId during login');
      } else {
        debugPrint(
          '❌ Cannot save FCM token - userId: ${userId != null}, token: ${token != null}',
        );
      }
    } catch (e) {
      debugPrint('❌ Failed to initialize notifications for user: $e');
    }
  }
}
