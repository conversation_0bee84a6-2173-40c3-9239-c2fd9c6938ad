import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mr_garments_mobile/services/notification_service.dart';

/// Notification state class
class NotificationState {
  final bool isInitialized;
  final bool hasPermission;
  final String? fcmToken;
  final String? error;
  final bool isLoading;

  const NotificationState({
    this.isInitialized = false,
    this.hasPermission = false,
    this.fcmToken,
    this.error,
    this.isLoading = false,
  });

  NotificationState copyWith({
    bool? isInitialized,
    bool? hasPermission,
    String? fcmToken,
    String? error,
    bool? isLoading,
  }) {
    return NotificationState(
      isInitialized: isInitialized ?? this.isInitialized,
      hasPermission: hasPermission ?? this.hasPermission,
      fcmToken: fcmToken ?? this.fcmToken,
      error: error,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

/// Notification provider
class NotificationNotifier extends StateNotifier<NotificationState> {
  NotificationNotifier() : super(const NotificationState());

  /// Initialize notifications
  Future<void> initialize() async {
    if (state.isInitialized) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      await NotificationService.initialize();
      state = state.copyWith(
        isInitialized: true,
        hasPermission: true,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Update user ID for notifications
  Future<void> updateUserId(String? userId) async {
    try {
      await NotificationService.updateUserId(userId);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    try {
      await NotificationService.clearAllNotifications();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Clear notifications for specific chat
  Future<void> clearChatNotifications(String chatId) async {
    try {
      await NotificationService.clearChatNotifications(chatId);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Dispose notifications
  @override
  void dispose() {
    NotificationService.dispose();
    super.dispose();
  }

  /// Cleanup notifications manually
  Future<void> cleanup() async {
    try {
      await NotificationService.dispose();
      state = const NotificationState();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Notification provider
final notificationProvider =
    StateNotifierProvider<NotificationNotifier, NotificationState>((ref) {
      return NotificationNotifier();
    });

/// Helper method to initialize notifications for a user
Future<void> initializeNotificationsForUser(String userId) async {
  final container = ProviderContainer();
  final notificationNotifier = container.read(notificationProvider.notifier);

  await notificationNotifier.initialize();
  await notificationNotifier.updateUserId(userId);

  container.dispose();
}
