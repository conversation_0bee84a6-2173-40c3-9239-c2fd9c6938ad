import 'dart:convert';
import 'package:http/http.dart' as http;

class UserService {
  static const String baseUrl = "https://mrgarment.braincavesoft.com/api";

  static Future<List<dynamic>> fetchAllUsers() async {
    final response = await http.get(Uri.parse('$baseUrl/users'));
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to load users');
    }
  }

  static Future<List<dynamic>> fetchNewUserRequests() async {
    final response = await http.get(Uri.parse('$baseUrl/users/requests'));
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to load requests');
    }
  }

  static Future<Map<String, dynamic>> verifyUser(
    int userId,
    String action,
  ) async {
    final response = await http.post(
      Uri.parse('$baseUrl/users/verify'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({"userId": userId, "action": action}),
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to $action user');
    }
  }

  static Future<Map<String, dynamic>> addUser(Map<String, dynamic> user) async {
    final response = await http.post(
      Uri.parse('$baseUrl/users'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(user),
    );
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to add user');
    }
  }

  static Future<Map<String, dynamic>> updateUser(
    int userId,
    Map<String, dynamic> user,
  ) async {
    final response = await http.put(
      Uri.parse('$baseUrl/users/$userId'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(user),
    );
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to update user');
    }
  }

  static Future<Map<String, dynamic>> deactivateUser(int userId) async {
    final response = await http.patch(
      Uri.parse('$baseUrl/users/$userId/status'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({"status": "deactivated"}),
    );
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to deactivate user');
    }
  }

  // Staff Management Methods

  /// Register a new staff member for a company (manufacturer, retailer, or distributor)
  static Future<Map<String, dynamic>> registerStaff({
    required String name,
    required String email,
    required String mobile,
    required String password,
    required int companyId,
    required String companyType, // 'manufacturer', 'retailer', 'distributor'
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/staff/register'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'name': name,
        'email': email,
        'mobile_number': mobile,
        'password': password,
        'account_type': 'staff',
        'company_id': companyId,
        'company_type': companyType,
      }),
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to register staff member');
    }
  }

  /// Fetch all staff members for a specific company
  static Future<List<dynamic>> fetchStaffByCompany(int companyId) async {
    final response = await http.get(
      Uri.parse('$baseUrl/staff/company/$companyId'),
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to load staff members');
    }
  }

  /// Fetch all staff members for a specific manufacturer (backward compatibility)
  static Future<List<dynamic>> fetchStaffByManufacturer(
    int manufacturerId,
  ) async {
    return fetchStaffByCompany(manufacturerId);
  }

  /// Fetch all pending staff requests for admin approval
  static Future<List<dynamic>> fetchStaffRequests() async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/staff/requests'));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        // print('Staff requests fetched successfully: ${data.length} requests');
        return data;
      } else {
        // print(
        //   'Failed to fetch staff requests: ${response.statusCode} - ${response.body}',
        // );
        throw Exception(
          'Failed to load staff requests: ${response.statusCode}',
        );
      }
    } catch (e) {
      // print('Error fetching staff requests: $e');
      rethrow;
    }
  }

  /// Approve or reject a staff member request
  static Future<Map<String, dynamic>> verifyStaff(
    int staffId,
    String action,
  ) async {
    try {
      // print('Verifying staff: $staffId with action: $action');
      final response = await http.post(
        Uri.parse('$baseUrl/staff/verify'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({"staffId": staffId, "action": action}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        // print('Staff verification successful: $data');
        return data;
      } else {
        // print(
        //   'Failed to verify staff: ${response.statusCode} - ${response.body}',
        // );
        throw Exception(
          'Failed to $action staff member: ${response.statusCode}',
        );
      }
    } catch (e) {
      // print('Error verifying staff: $e');
      rethrow;
    }
  }

  /// Update staff member details 
  static Future<Map<String, dynamic>> updateStaff(
    int staffId,
    Map<String, dynamic> staffData,
  ) async {
    final response = await http.put(
      Uri.parse('$baseUrl/staff/$staffId'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(staffData),
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to update staff member');
    }
  }

  /// Deactivate a staff member
  static Future<Map<String, dynamic>> deactivateStaff(int staffId) async {
    final response = await http.patch(
      Uri.parse('$baseUrl/staff/$staffId/status'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({"status": "deactivated"}),
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to deactivate staff member');
    }
  }
}
