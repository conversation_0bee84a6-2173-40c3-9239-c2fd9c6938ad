import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';

class BrandService {
  static const String baseUrl = 'https://mrgarment.braincavesoft.com/api';

  static Future<List<Map<String, dynamic>>> fetchBrands() async {
    final response = await http.get(Uri.parse('$baseUrl/brands'));

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data.cast<Map<String, dynamic>>();
    } else {
      throw Exception('Failed to load brands');
    }
  }

  // Get brands for specific category
  static Future<List<Map<String, dynamic>>> fetchBrandsByCategory(
    int categoryId,
  ) async {
    final response = await http.get(
      Uri.parse('$baseUrl/categories/$categoryId/brands'),
    );
    if (response.statusCode == 200) {
      final List data = json.decode(response.body);
      return data.cast<Map<String, dynamic>>();
    } else {
      throw Exception('Failed to load brands for category ID $categoryId');
    }
  }

  // Get catalogs for specific brand
  static Future<List<Map<String, dynamic>>> fetchCatalogsByBrand(
    int brandId,
  ) async {
    final response = await http.get(
      Uri.parse('$baseUrl/brands/$brandId/catalogs'),
    );
    if (response.statusCode == 200) {
      final List data = json.decode(response.body);
      return data.cast<Map<String, dynamic>>();
    } else {
      throw Exception('Failed to load catalogs for brand ID $brandId');
    }
  }

  static Future<Map<String, dynamic>> addBrand({
    required String brandName,
    required String manufacturerName,
    File? imageFile,
  }) async {
    final uri = Uri.parse('$baseUrl/brands');
    final request =
        http.MultipartRequest('POST', uri)
          ..fields['brandName'] = brandName
          ..fields['manufacturerName'] = manufacturerName;

    if (imageFile != null) {
      final mimeType = 'image/${imageFile.path.split('.').last}';
      request.files.add(
        await http.MultipartFile.fromPath(
          'images',
          imageFile.path,
          contentType: MediaType.parse(mimeType),
        ),
      );
    }

    final response = await request.send();

    if (response.statusCode == 201) {
      final resBody = await response.stream.bytesToString();
      return json.decode(resBody);
    } else {
      throw Exception('Failed to add brand');
    }
  }
}
