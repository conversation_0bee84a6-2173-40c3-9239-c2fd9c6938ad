import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

class CategoryService {
  static const String baseUrl = 'https://mrgarment.braincavesoft.com/api';

  // GET: All Categories
  static Future<List<Map<String, dynamic>>> fetchCategories() async {
    final response = await http.get(Uri.parse('$baseUrl/categories'));

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data.cast<Map<String, dynamic>>();
    } else {
      throw Exception('Failed to fetch categories');
    }
  }

  // GET: Category Details by ID
  static Future<Map<String, dynamic>> fetchCategoryDetails(int id) async {
    final response = await http.get(Uri.parse('$baseUrl/categories/$id'));

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to fetch category details');
    }
  }

  // POST: Add New Category
  static Future<bool> addCategory(Map<String, dynamic> data) async {
    final uri = Uri.parse('$baseUrl/categories');
    var request = http.MultipartRequest('POST', uri);

    request.fields['categoryName'] = data['categoryName'];
    request.fields['parentCategoryName'] = data['parentCategoryName'];
    request.fields['subCategoryName'] = data['subCategoryName'];
    request.fields['categorySlug'] = data['categorySlug'];
    request.fields['description'] = data['description'];
    request.fields['displayType'] = data['displayType'];

    if (data['image'] != null && data['image'] is File) {
      request.files.add(
        await http.MultipartFile.fromPath('image', data['image'].path),
      );
    }

    final response = await request.send();
    if (response.statusCode == 201) {
      return true;
    } else {
      return false;
    }
  }

  // GET: Catalogs by Category ID
  static Future<List<Map<String, dynamic>>> fetchCatalogsByCategory(
    int categoryId,
  ) async {
    final response = await http.get(
      Uri.parse('$baseUrl/categories/$categoryId/catalogs'),
    );

    if (response.statusCode == 200) {
      final List data = json.decode(response.body);
      return data.cast<Map<String, dynamic>>();
    } else {
      throw Exception('Failed to fetch catalogs for category');
    }
  }
}
