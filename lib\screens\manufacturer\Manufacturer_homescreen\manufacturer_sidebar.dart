import 'package:flutter/material.dart';
// import 'package:mr_garments_mobile/screens/manufacturer/manufacturer_tabpage_view.dart';
import 'package:mr_garments_mobile/screens/common/generic_sidebar.dart';

class ManufacturerSidebar extends StatelessWidget {
  const ManufacturerSidebar({super.key});

  @override
  Widget build(BuildContext context) {
    return GenericSidebar(
      userType: 'manufacturer',
      // customMenuItems: [
      //   SidebarMenuItem(
      //     icon: Icons.business,
      //     title: "Manufacturers",
      //     subtitle: "View all manufacturers",
      //     onTap: () {
      //       Navigator.push(
      //         context,
      //         MaterialPageRoute(
      //           builder: (_) => const ManufacturerTabpageView(),
      //         ),
      //       );
      //     },
    //     ),
    //   ],
    );
  }
}
