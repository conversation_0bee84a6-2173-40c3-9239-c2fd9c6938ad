# 🚀 Firebase Cloud Functions Notification Setup

Complete guide to implement WhatsApp-like notifications using Firebase Cloud Functions for the MR Garments chat app.

## 📋 Prerequisites

- Firebase project: `mrgarments-f3b34` ✅
- Service account JSON: `mrgarments-f3b34-7687b72bb01d.json` ✅
- Node.js installed on your machine
- Firebase CLI access

## 🛠️ Step-by-Step Setup

### Step 1: Install Firebase CLI

```bash
# Install Firebase CLI globally
npm install -g firebase-tools

# Verify installation
firebase --version
```

### Step 2: Login and Initialize

```bash
# Login to Firebase
firebase login

# Navigate to your project directory (create a new folder for functions)
mkdir mr-garments-functions
cd mr-garments-functions

# Initialize Firebase Functions
firebase init functions
```

**During initialization:**
- Select "Use an existing project" → `mrgarments-f3b34`
- Choose "JavaScript" (recommended for simplicity)
- Install dependencies with npm → **Yes**
- Do you want to install dependencies now? → **Yes**

### Step 3: Install Required Dependencies

```bash
cd functions
npm install firebase-admin firebase-functions
```

### Step 4: Replace functions/index.js

Replace the content of `functions/index.js` with the Cloud Function code:

```javascript
const functions = require('firebase-functions');
const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
admin.initializeApp();

/**
 * Cloud Function to send FCM notifications for chat messages
 */
exports.sendChatNotification = functions.https.onRequest(async (req, res) => {
  // Set CORS headers
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }

  if (req.method !== 'POST') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    const { memberIds, chatId, senderName, messageText, messageType, senderId } = req.body;

    // Validation
    if (!memberIds || !Array.isArray(memberIds) || memberIds.length === 0) {
      res.status(400).json({ error: 'memberIds is required and must be a non-empty array' });
      return;
    }

    if (!chatId || !senderName || !messageText || !messageType || !senderId) {
      res.status(400).json({ 
        error: 'chatId, senderName, messageText, messageType, and senderId are required' 
      });
      return;
    }

    console.log('Sending notification for chat:', chatId, 'from:', senderName);

    // Get FCM tokens from Firestore
    const fcmTokens = await getFCMTokensForMembers(memberIds);

    if (fcmTokens.length === 0) {
      res.status(404).json({ error: 'No FCM tokens found for the specified members' });
      return;
    }

    // Send notifications
    const notificationTitle = senderName;
    const notificationBody = messageType === 'image' ? '📷 Photo' : messageText;

    const results = await Promise.allSettled(
      fcmTokens.map(token => sendNotificationToToken(
        token,
        notificationTitle,
        notificationBody,
        {
          chatId,
          otherUserName: senderName,
          isGroup: 'false',
          click_action: 'FLUTTER_NOTIFICATION_CLICK',
        }
      ))
    );

    const successful = results.filter(result => result.status === 'fulfilled').length;
    const failed = results.filter(result => result.status === 'rejected').length;

    console.log(`Notification results: ${successful} successful, ${failed} failed`);

    res.status(200).json({
      success: true,
      message: 'Notifications processed',
      results: { total: fcmTokens.length, successful, failed }
    });

  } catch (error) {
    console.error('Error sending notifications:', error);
    res.status(500).json({ error: 'Internal server error', message: error.message });
  }
});

/**
 * Get FCM tokens for given member IDs from Firestore
 */
async function getFCMTokensForMembers(memberIds) {
  try {
    const tokens = [];
    const db = admin.firestore();

    for (const memberId of memberIds) {
      try {
        const userDoc = await db.collection('chat_users').doc(memberId).get();
        if (userDoc.exists) {
          const userData = userDoc.data();
          if (userData.fcmToken) {
            tokens.push(userData.fcmToken);
          }
        }
      } catch (error) {
        console.error(`Error getting FCM token for user ${memberId}:`, error);
      }
    }

    return tokens;
  } catch (error) {
    console.error('Error getting FCM tokens:', error);
    return [];
  }
}

/**
 * Send notification to a specific FCM token
 */
async function sendNotificationToToken(fcmToken, title, body, data) {
  try {
    const message = {
      token: fcmToken,
      notification: { title: title, body: body },
      data: data,
      android: {
        notification: {
          sound: 'default',
          channelId: 'mr_garments_chat',
          priority: 'high',
        },
      },
      apns: {
        payload: {
          aps: { sound: 'default', badge: 1 },
        },
      },
    };

    const response = await admin.messaging().send(message);
    console.log('Successfully sent message to token:', fcmToken.substring(0, 20) + '...');
    return { success: true, messageId: response };

  } catch (error) {
    console.error('Error sending to token:', fcmToken.substring(0, 20) + '...', error);
    
    if (error.code === 'messaging/invalid-registration-token' || 
        error.code === 'messaging/registration-token-not-registered') {
      console.log('Invalid token detected, should be removed from database');
    }
    
    throw error;
  }
}
```

### Step 5: Deploy the Function

```bash
# Deploy the function
firebase deploy --only functions

# Note the function URL from the output (should be):
# https://us-central1-mrgarments-f3b34.cloudfunctions.net/sendChatNotification
```

### Step 6: Verify Deployment

```bash
# Check function status
firebase functions:list

# View function logs
firebase functions:log --only sendChatNotification
```

## 🧪 Testing

### Test with curl

```bash
curl -X POST https://us-central1-mrgarments-f3b34.cloudfunctions.net/sendChatNotification \
  -H "Content-Type: application/json" \
  -d '{
    "memberIds": ["test_user_1", "test_user_2"],
    "chatId": "test_chat_123",
    "senderName": "Test Sender",
    "messageText": "Hello from Cloud Function!",
    "messageType": "text",
    "senderId": "sender_123"
  }'
```

### Test from Flutter App

```dart
import 'package:mr_garments_mobile/utils/notification_test_helper.dart';

// Test the complete flow
NotificationTestHelper.runComprehensiveTest();

// Test Cloud Function specifically
NotificationTestHelper.testBackendNotification();
```

## 📊 Monitoring

### View Logs

```bash
# Real-time logs
firebase functions:log --follow

# Specific function logs
firebase functions:log --only sendChatNotification

# Logs with timestamps
firebase functions:log --since 1h
```

### Firebase Console

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select project: `mrgarments-f3b34`
3. Navigate to **Functions** → **Logs**
4. Monitor executions and errors

## 🔧 Configuration

### Environment Variables (Optional)

```bash
# Set custom config
firebase functions:config:set notification.title="MR Garments"

# Get config
firebase functions:config:get

# Use in function
const config = functions.config();
const appTitle = config.notification.title || 'MR Garments';
```

## ✅ Verification Checklist

- [ ] Firebase CLI installed and logged in
- [ ] Functions initialized for `mrgarments-f3b34`
- [ ] Dependencies installed (`firebase-admin`, `firebase-functions`)
- [ ] Function code deployed successfully
- [ ] Function URL accessible
- [ ] Client app updated with correct function URL
- [ ] Test notifications working
- [ ] Logs showing successful executions

## 🚨 Troubleshooting

### Common Issues

1. **"Permission denied"**
   ```bash
   firebase login --reauth
   firebase use mrgarments-f3b34
   ```

2. **"Function not found"**
   ```bash
   firebase deploy --only functions
   firebase functions:list
   ```

3. **"No FCM tokens found"**
   - Ensure users are logged in and FCM tokens are saved
   - Check Firestore collection `chat_users`

4. **"CORS errors"**
   - CORS headers are already set in the function
   - Ensure you're making POST requests

### Debug Steps

1. Check function logs: `firebase functions:log`
2. Verify Firestore data structure
3. Test with curl first
4. Check client-side error messages
5. Monitor Firebase Console

## 🎉 Success!

Your Cloud Function is now ready to send WhatsApp-like notifications! The function will:

- ✅ Receive notification requests from your Flutter app
- ✅ Get FCM tokens from Firestore
- ✅ Send push notifications using Firebase Admin SDK
- ✅ Handle errors gracefully
- ✅ Provide detailed logging for monitoring

No server key required, fully secure, and serverless! 🚀
