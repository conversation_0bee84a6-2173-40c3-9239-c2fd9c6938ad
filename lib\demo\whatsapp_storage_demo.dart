import 'dart:io';
import '../services/whatsapp_local_storage_service.dart';

/// Demo file to demonstrate WhatsApp-like local storage functionality
///
/// This demonstrates the complete flow:
/// 1. Sender sends images → stored in Android/media/com.mrgarments/media/Images/Sent/
/// 2. Images uploaded to Firebase storage
/// 3. Receiver receives images → stored in Android/media/com.mrgarments/media/Images/Private/
/// 4. Firebase images are deleted after successful receiver storage
///
/// Features:
/// - Encrypted file naming for security
/// - All images saved in .jpg format
/// - Automatic thumbnail generation
/// - WhatsApp-like directory structure
/// - Firebase cleanup after receiver storage

/// Demo function to test WhatsApp Local Storage initialization
Future<void> demoInitializeWhatsAppLocalStorage() async {
  try {
    print('🚀 Initializing WhatsApp Local Storage...');

    // Test initialization
    await WhatsAppLocalStorageService.initialize();

    // Check if directories are created
    final senderPath = WhatsAppLocalStorageService.senderDirectoryPath;
    final receiverPath = WhatsAppLocalStorageService.receiverDirectoryPath;

    if (senderPath != null && receiverPath != null) {
      print('✅ WhatsApp Local Storage initialized successfully');
      print('📁 Sender directory: $senderPath');
      print('📁 Receiver directory: $receiverPath');

      // Check if directories exist
      final senderDir = Directory(senderPath);
      final receiverDir = Directory(receiverPath);

      print('📂 Sender directory exists: ${await senderDir.exists()}');
      print('📂 Receiver directory exists: ${await receiverDir.exists()}');
    } else {
      print('❌ Failed to initialize WhatsApp Local Storage');
    }
  } catch (e) {
    print('❌ Error initializing WhatsApp Local Storage: $e');
  }
}

/// Demo function to test storing an image as sender
Future<void> demoStoreImageAsSender() async {
  try {
    print('\n📤 Testing sender image storage...');

    // Create a dummy image file for testing
    final tempDir = Directory.systemTemp;
    final testImageFile = File('${tempDir.path}/test_image.png');

    // Create a simple test image (1x1 pixel PNG)
    await testImageFile.writeAsBytes([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG header
      0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
      0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, // 1x1 dimensions
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53,
      0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, // IDAT chunk
      0x54, 0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x37, 0x6E, 0xF9, 0x24, 0x00, 0x00,
      0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, // IEND chunk
      0x60, 0x82,
    ]);

    // Test storing image as sender
    final result = await WhatsAppLocalStorageService.storeImageAsSender(
      sourceFile: testImageFile,
      messageId: 'demo_message_123',
      chatId: 'demo_chat_456',
    );

    if (result.success) {
      print('✅ Sender storage test passed');
      print('📤 Encrypted filename: ${result.encryptedFileName}');
      print('📁 Local path: ${result.localImagePath}');
      print('🖼️ Thumbnail path: ${result.thumbnailPath}');

      // Verify files exist
      if (result.localImagePath != null) {
        final exists = await File(result.localImagePath!).exists();
        print('📂 Image file exists: $exists');
      }

      if (result.thumbnailPath != null) {
        final exists = await File(result.thumbnailPath!).exists();
        print('📂 Thumbnail file exists: $exists');
      }
    } else {
      print('❌ Sender storage test failed: ${result.error}');
    }

    // Clean up test file
    await testImageFile.delete();
  } catch (e) {
    print('❌ Error in sender storage test: $e');
  }
}

/// Demo function to get storage information
Future<void> demoGetStorageInfo() async {
  try {
    print('\n📊 Getting storage information...');

    final storageInfo = await WhatsAppLocalStorageService.getStorageInfo();

    print('✅ Storage info retrieved successfully');
    print('📊 Total size: ${storageInfo.formattedTotalSize}');
    print('📊 Total files: ${storageInfo.totalCount}');
    print(
      '📤 Sender: ${storageInfo.formattedSenderSize} (${storageInfo.senderCount} files)',
    );
    print(
      '📥 Receiver: ${storageInfo.formattedReceiverSize} (${storageInfo.receiverCount} files)',
    );
  } catch (e) {
    print('❌ Error getting storage info: $e');
  }
}

/// Demo function to test cleanup
Future<void> demoCleanup() async {
  try {
    print('\n🧹 Testing cleanup functionality...');

    // Test cleanup functionality
    await WhatsAppLocalStorageService.cleanupOldFiles(daysOld: 30);

    print('✅ Cleanup test completed');
    print('🧹 Old files (30+ days) cleaned up');
  } catch (e) {
    print('❌ Error in cleanup test: $e');
  }
}

/// Main demo function that runs all tests
Future<void> runWhatsAppStorageDemo() async {
  print('🎯 WHATSAPP-LIKE STORAGE DEMO');
  print('=' * 50);

  await demoInitializeWhatsAppLocalStorage();
  await demoStoreImageAsSender();
  await demoGetStorageInfo();
  await demoCleanup();

  print('\n🎉 Demo completed!');
  print('=' * 50);
}

/// Function to demonstrate the complete flow
void demonstrateWhatsAppFlow() {
  print('\n📱 MR GARMENTS WHATSAPP-LIKE IMAGE STORAGE');
  print('=' * 50);

  print('\n🏗️ ARCHITECTURE (Like WhatsApp):');
  print('├── Android/media/');
  print('│   └── com.mrgarments/');
  print('│       └── media/');
  print('│           └── Images/');
  print('│               ├── Sent/           # Images sent by user');
  print('│               │   ├── abc123.jpg  # Encrypted filename');
  print('│               │   └── thumb_abc123.jpg');
  print('│               └── Private/        # Images received from others');
  print('│                   ├── def456.jpg  # Same encryption as sender');
  print('│                   └── thumb_def456.jpg');
  print('├── Firebase Storage                # Temporary cloud storage');
  print(
    '└── Auto cleanup                    # Removes cloud files after download',
  );

  print('\n🔄 FLOW:');
  print('1. Sender: Image → Local Storage → Firebase → Receiver notification');
  print('2. Receiver: Download → Local Storage → Firebase cleanup');
  print('3. Result: Both users have images locally, no cloud storage');

  print('\n🔒 SECURITY:');
  print('• Encrypted filenames prevent identification');
  print('• Automatic Firebase cleanup ensures privacy');
  print('• Local storage only accessible by app');

  print('\n⚡ PERFORMANCE:');
  print('• Instant display from local storage');
  print('• Background upload/download');
  print('• Minimal cloud storage usage');
  print('• WhatsApp-like user experience');

  print('\n✅ IMPLEMENTATION COMPLETE!');
  print('=' * 50);
}

/// Entry point for running the demo
void main() async {
  demonstrateWhatsAppFlow();
  await runWhatsAppStorageDemo();
}
