import 'dart:convert';

import 'package:http/http.dart' as http;

class RetailerService {
  static const String baseUrl = 'https://mrgarment.braincavesoft.com/api';

  // Fetch all retailers
  static Future<List<dynamic>> fetchAllRetailers() async {
    final response = await http.get(Uri.parse('$baseUrl/retailers'));
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to load retailers');
    }
  }

  // Get retailer details by ID
  static Future<Map<String, dynamic>> fetchRetailerDetails(
    int retailerId,
  ) async {
    final response = await http.get(
      Uri.parse('$baseUrl/retailers/$retailerId'),
    );
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to fetch retailer details');
    }
  }

  // deactivate status
  static Future<Map<String, dynamic>> deactivateRetailer(int id) async {
    final response = await http.patch(
      Uri.parse('$baseUrl/retailers/$id/status'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({"status": "deactivated"}),
    );
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to deactivate retailer');
    }
  }

  // add retailer
  static Future<Map<String, dynamic>> addRetailer(
    Map<String, dynamic> retailer,
  ) async {
    final response = await http.post(
      Uri.parse('$baseUrl/retailers'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(retailer),
    );
    if (response.statusCode == 200 || response.statusCode == 201) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to add retailer');
    }
  }

  // edit retailer
  static Future<Map<String, dynamic>> updateRetailer(
    int id,
    Map<String, dynamic> retailer,
  ) async {
    final response = await http.put(
      Uri.parse('$baseUrl/retailers/$id'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(retailer),
    );
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to update retailer');
    }
  }

  // Fetch retailers by distributor ID
  static Future<List<dynamic>> fetchRetailersByDistributor(
    int distributorId,
  ) async {
    // print('Fetching retailers for distributor: $distributorId');

    final response = await http.get(
      Uri.parse('$baseUrl/retailers/distributor/$distributorId'),
    );

    // print(
    //   'Fetch retailers response: ${response.statusCode} - ${response.body}',
    // );

    if (response.statusCode == 200) {
      final retailers = json.decode(response.body);
      // print(
      //   'Found ${retailers.length} retailers for distributor $distributorId',
      // );
      return retailers;
    } else {
      throw Exception(
        'Failed to load retailers for distributor: ${response.statusCode} - ${response.body}',
      );
    }
  }

  // Add retailer for a specific distributor
  static Future<Map<String, dynamic>> addRetailerForDistributor({
    required String name,
    required String email,
    required String mobile,
    required String password,
    required String companyName,
    required String address,
    required int distributorId,
  }) async {
    final requestBody = {
      'name': name,
      'email': email,
      'mobile_number': mobile,
      'password': password,
      'company_name': companyName,
      'address': address,
      'account_type': 'retailer',
      'distributor_id': distributorId,
      'status': 'pending', // Retailers need admin approval
    };

    // print('Adding retailer with data: $requestBody');

    final response = await http.post(
      Uri.parse('$baseUrl/retailers/request'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(requestBody),
    );

    // print('Add retailer response: ${response.statusCode} - ${response.body}');

    if (response.statusCode == 200 || response.statusCode == 201) {
      return json.decode(response.body);
    } else {
      throw Exception(
        'Failed to add retailer for distributor: ${response.statusCode} - ${response.body}',
      );
    }
  }

  // Fetch all retailer requests for admin approval
  static Future<List<dynamic>> fetchRetailerRequests() async {
    final response = await http.get(Uri.parse('$baseUrl/retailers/requests'));
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to load retailer requests');
    }
  }

  // Approve or reject a retailer request
  static Future<Map<String, dynamic>> verifyRetailer(
    int retailerId,
    String action,
  ) async {
    try {
      // print('Verifying retailer: $retailerId with action: $action');
      final response = await http.post(
        Uri.parse('$baseUrl/retailers/verify'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({"retailerId": retailerId, "action": action}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        // print('Retailer verification successful: $data');
        return data;
      } else {
        // print(
        //   'Failed to verify retailer: ${response.statusCode} - ${response.body}',
        // );
        throw Exception('Failed to $action retailer: ${response.statusCode}');
      }
    } catch (e) {
      // print('Error verifying retailer: $e');
      rethrow;
    }
  }
}
