import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ViewOrderScreen extends StatelessWidget {
  final Map<String, dynamic> order;

  const ViewOrderScreen({super.key, required this.order});

  Widget _infoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(
            "$label: ",
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.poppins(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final items = order['items'] as List<Map<String, dynamic>>?;
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF005368),
        foregroundColor: Colors.white,
        elevation: 0,
        title: Text(
          "View Order",
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              //  implement share functionality
            },
            icon: const Icon(Icons.share),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Order details
            Card(
              elevation: 3,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _infoRow("Order Number", order['orderId']?.toString() ?? ''),
                    _infoRow("Order Date", order['orderDate']?.toString() ?? ''),
                    _infoRow("Serial Number", order['serialNumber']?.toString() ?? ''),
                    _infoRow("Customer", order['customerName']?.toString() ?? ''),
                    _infoRow("Address", order['customerAddress']?.toString() ?? ''),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Order items
            Text(
              "Items",
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF005368),
              ),
            ),
            const SizedBox(height: 10),
            if (items == null || items.isEmpty)
              const Text(
                "No items found.",
                style: TextStyle(color: Colors.grey),
              )
            else
              ...List.generate(items.length, (index) {
                final item = items[index];
                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Item: ${item['name']?.toString() ?? ''}",
                        style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 4),
                      Text("Quantity: ${item['quantity']?.toString() ?? ''}"),
                      Text("Amount: ${item['amount']?.toString() ?? ''}"),
                    ],
                  ),
                );
              }),

            const SizedBox(height: 20),

            // Order totals
            Card(
              elevation: 3,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _infoRow("Total", order['total']?.toString() ?? ''),
                    _infoRow("GST", order['gst']?.toString() ?? ''),
                    _infoRow("Discount", order['discount']?.toString() ?? '0'),
                    const Divider(thickness: 1),
                    _infoRow(
                      "Grand Total",
                      order['grandTotal']?.toString() ?? '',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
