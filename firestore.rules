rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // ==================== USERS COLLECTION ====================
    match /users/{userId} {
      // Allow authenticated users to read/write user documents
      // This is needed for chat member information and Laravel user mapping
      allow read, write: if request.auth != null;
    }

    // ==================== CHATS COLLECTION ====================
    match /chats/{chatId} {
      // Allow authenticated users to read/write chats
      // We'll rely on application-level security since we're using Laravel user IDs
      // and the chat ID generation ensures privacy (userId1_userId2 format)
      allow read, write: if request.auth != null;

      // ==================== MESSAGES SUBCOLLECTION ====================
      match /messages/{messageId} {
        // Allow authenticated users to read/write messages
        // Privacy is ensured by the chat ID structure and application logic
        allow read, write: if request.auth != null;
      }
    }

    // ==================== GROUPS COLLECTION ====================
    match /groups/{groupId} {
      // Allow read/write for authenticated users
      // Group membership is checked at application level
      allow read, write: if request.auth != null;
    }
  }
}
