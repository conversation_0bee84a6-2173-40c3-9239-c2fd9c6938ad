import 'dart:convert';
import 'package:http/http.dart' as http;

class ManufacturerService {
  static const String baseUrl = "https://mrgarment.braincavesoft.com/api";

  /// 1) Fetch all manufacturers
  static Future<List<dynamic>> fetchAllManufacturers() async {
    final response = await http.get(Uri.parse('$baseUrl/manufacturers')); 
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to load manufacturers');
    }
  }

  /// 2) Add a new manufacturer
  static Future<Map<String, dynamic>> addManufacturer(Map<String, dynamic> manufacturer) async {
    final response = await http.post(
      Uri.parse('$baseUrl/manufacturers'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(manufacturer),
    );
    if (response.statusCode == 200  || response.statusCode == 201) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to add manufacturer: ${response.statusCode} ${response.body}');
    }
  }

  /// 3) Update manufacturer by ID
  static Future<Map<String, dynamic>> updateManufacturer(int manufacturerId, Map<String, dynamic> manufacturer) async {
    final response = await http.put(
      Uri.parse('$baseUrl/manufacturers/$manufacturerId'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(manufacturer),
    );
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to update manufacturer: ${response.statusCode} ${response.body}');
    }
  }

  static Future<Map<String, dynamic>> getManufacturerDetails(int id) async {
  final response = await http.get(Uri.parse('$baseUrl/manufacturers/$id'));
  if (response.statusCode == 200) {
    return json.decode(response.body);
  } else {
    throw Exception('Failed to fetch manufacturer details');
  }
}


  /// 4) Deactivate manufacturer by ID (PATCH status)
  static Future<Map<String, dynamic>> deactivateManufacturer(int manufacturerId) async {
    final response = await http.patch(
      Uri.parse('$baseUrl/manufacturers/$manufacturerId/status'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({"status": "deactivated"}),
    );
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to deactivate manufacturer');
    }
  }

  /// 5) Get manufacturer details by ID
static Future<Map<String, dynamic>> fetchManufacturerDetails(int manufacturerId) async {
  final response = await http.get(Uri.parse('$baseUrl/manufacturers/$manufacturerId'));
  if (response.statusCode == 200) {
    return json.decode(response.body);
  } else {
    throw Exception('Failed to fetch manufacturer details: ${response.statusCode} ${response.body}');
  }
}

}
