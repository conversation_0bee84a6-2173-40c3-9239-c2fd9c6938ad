import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/utils/enhanced_image_cache.dart';

/// Utility functions for handling image URLs and processing
/// Now enhanced with WhatsApp-like caching behavior
class ImageUtils {
  /// Cleans malformed image URLs that have double storage paths
  ///
  /// This fixes URLs like:
  /// https://mrgarment.braincavesoft.com/storage/https://mrgarment.braincavesoft.com/storage/catalogs/...
  ///
  /// And converts them to:
  /// https://mrgarment.braincavesoft.com/storage/catalogs/...
  static String cleanImageUrl(String url) {
    return EnhancedImageCache.cleanImageUrl(url);
  }

  /// Cleans a list of image URLs
  static List<String> cleanImageUrls(List<String> urls) {
    return urls.map((url) => cleanImageUrl(url)).toList();
  }

  /// Builds a cached network image widget with enhanced caching
  static Widget buildNetworkImage(
    String imageUrl, {
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
    BorderRadius? borderRadius,
  }) {
    return EnhancedImageCache.buildCachedImage(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      borderRadius: borderRadius,
    );
  }

  /// Builds a cached network image widget specifically for chat messages
  /// Now with enhanced caching and preloading
  static Widget buildChatImage(
    String imageUrl, {
    required double width,
    required double height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    VoidCallback? onTap,
  }) {
    return EnhancedImageCache.buildChatImage(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      borderRadius: borderRadius,
      onTap: onTap,
    );
  }

  /// Builds a cached network image widget specifically for catalog images
  static Widget buildCatalogImage(
    String imageUrl, {
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
    BorderRadius? borderRadius,
  }) {
    return EnhancedImageCache.buildCachedImage(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      borderRadius: borderRadius,
      enableMemoryCache: true,
    );
  }

  /// Preload images for better performance with enhanced caching
  /// This is useful for preloading chat images when opening a chat
  static Future<void> preloadChatImages(List<String> imageUrls) async {
    await EnhancedImageCache.preloadImages(imageUrls);
  }

  /// Preload a single image with enhanced caching
  static Future<void> preloadImage(String imageUrl) async {
    await EnhancedImageCache.preloadImage(imageUrl);
  }

  /// Check if an image is cached (both memory and disk)
  static Future<bool> isImageCached(String imageUrl) async {
    return await EnhancedImageCache.isImageCached(imageUrl);
  }

  /// Preload images with progress callback
  static Future<void> preloadImagesWithProgress(
    List<String> imageUrls, {
    Function(int loaded, int total)? onProgress,
    VoidCallback? onComplete,
  }) async {
    await EnhancedImageCache.preloadImages(
      imageUrls,
      onProgress: onProgress,
      onComplete: onComplete,
    );
  }

  /// Get cache information for debugging
  static Future<Map<String, dynamic>> getCacheInfo() async {
    return await EnhancedImageCache.getCacheInfo();
  }

  /// Clear memory cache (keep disk cache for offline access)
  static void clearMemoryCache() {
    EnhancedImageCache.clearMemoryCache();
  }

  /// Clear all caches (use sparingly)
  static Future<void> clearAllCache() async {
    await EnhancedImageCache.clearAllCache();
  }
}