import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/providers/retailer_provider.dart';
import 'package:mr_garments_mobile/screens/retailer/add_edit_retailer.dart';
import 'package:mr_garments_mobile/screens/retailer/retailer_details.dart';

class RetailerUserlist extends ConsumerWidget {
  final String searchQuery;
  const RetailerUserlist({super.key, required this.searchQuery});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final retailerState = ref.watch(retailersProvider);

    return retailerState.retailers.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, _) => Center(child: Text("Error: $e")),
      data: (retailers) {
        final activeRetailers =
            retailers
                .where((r) => r['status']?.toLowerCase() != 'deactivated')
                .toList();
        // apply search filter here
        final filteredRetailers =
            activeRetailers.where((r) {
              final name = (r['name'] ?? '').toLowerCase();
              return name.contains(searchQuery.toLowerCase());
            }).toList();
        if (filteredRetailers.isEmpty) {
          return const Center(child: Text('No retailers found'));
        }
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount:
              filteredRetailers.length, // Replace with dynamic count from API
          itemBuilder: (context, index) {
            final retailer = filteredRetailers[index];
            return GestureDetector(
              onTap: () {
                Navigator.push(
                  context, 
                  MaterialPageRoute(
                    builder: (context) => RetailerDetails(retailerId:retailer['id'],),
                  ),
                );
              },
              child: Card(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                elevation: 6,
                shadowColor: Colors.black26,
                margin: const EdgeInsets.only(bottom: 16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      /// Header with name and edit icon
                      Row(
                        children: [
                          const CircleAvatar(
                            // radius: 18,
                            backgroundColor: Color(0xFF005368),
                            child: Icon(Icons.store, color: Colors.white),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  retailer['name'] ?? '',
                                  style: GoogleFonts.poppins(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                  ),
                                ),
                                Text(
                                  retailer['email'] ?? '',
                                  style: GoogleFonts.poppins(
                                    fontSize: 13,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                            onPressed: () {
                              // Edit retailer info
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => AddEditRetailer(
                                    retailerId: retailer['id'],
                                  ),
                                ),
                              );
                            },
                            icon: const Icon(
                              Icons.edit,
                              color: Color(0xFFF2A738),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          const Icon(
                            Icons.location_on,
                            size: 18,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Text(
                              retailer['address'] ?? '',
                              style: GoogleFonts.poppins(
                                fontSize: 13,
                                color: Colors.grey[800],
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      Row(
                        children: [
                          const Icon(
                            Icons.phone_android,
                            size: 18,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            retailer['mobile'] ?? '',
                            style: GoogleFonts.poppins(
                              fontSize: 13,
                              color: Colors.grey[800],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _infoBox(
                            label: "Catalog Count",
                            value: retailer['catalogCount'].toString(),
                          ),
                          _infoBox(
                            label: "Credit Limit",
                            value: "₹${retailer['creditLimit']}",
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _infoBox({required String label, required String value}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: const Color.fromARGB(128, 225, 239, 247),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(fontSize: 12, color: Colors.black54),
          ),
          const SizedBox(height: 4),
          Text(value, style: GoogleFonts.poppins(fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }
}
