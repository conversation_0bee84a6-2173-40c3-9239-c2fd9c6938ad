import 'package:flutter_test/flutter_test.dart';
import 'package:mr_garments_mobile/models/message.dart';

void main() {
  group('Grouped Image Selection Tests', () {
    test('Should select all images in a group when one is selected', () {
      final now = DateTime.now();
      
      // Create a group of 4 images sent within 1 minute
      final messages = [
        Message(
          id: 'img1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image1.jpg',
          timestamp: now,
        ),
        Message(
          id: 'img2',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image2.jpg',
          timestamp: now.add(const Duration(seconds: 10)),
        ),
        Message(
          id: 'img3',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image3.jpg',
          timestamp: now.add(const Duration(seconds: 20)),
        ),
        Message(
          id: 'img4',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image4.jpg',
          timestamp: now.add(const Duration(seconds: 30)),
        ),
        // Add a text message to ensure it's not included
        Message(
          id: 'text1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.text,
          text: 'Hello',
          timestamp: now.add(const Duration(seconds: 40)),
        ),
      ];

      // Simulate the getImageGroupMessages logic
      final targetMessage = messages[1]; // Select the second image
      final groupMessages = _getImageGroupMessages(messages, targetMessage);

      // Should return all 4 image messages, not just the selected one
      expect(groupMessages.length, equals(4));
      expect(groupMessages.every((msg) => msg.type == MessageType.image), isTrue);
      expect(groupMessages.map((msg) => msg.id).toList(), 
             containsAll(['img1', 'img2', 'img3', 'img4']));
      
      // Should not include the text message
      expect(groupMessages.any((msg) => msg.id == 'text1'), isFalse);
    });

    test('Should handle single image message', () {
      final now = DateTime.now();
      
      final messages = [
        Message(
          id: 'img1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image1.jpg',
          timestamp: now,
        ),
      ];

      final targetMessage = messages[0];
      final groupMessages = _getImageGroupMessages(messages, targetMessage);

      expect(groupMessages.length, equals(1));
      expect(groupMessages[0].id, equals('img1'));
    });

    test('Should not group images from different senders', () {
      final now = DateTime.now();
      
      final messages = [
        Message(
          id: 'img1',
          senderId: 'user1',
          senderName: 'User 1',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image1.jpg',
          timestamp: now,
        ),
        Message(
          id: 'img2',
          senderId: 'user2', // Different sender
          senderName: 'User 2',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image2.jpg',
          timestamp: now.add(const Duration(seconds: 10)),
        ),
      ];

      final targetMessage = messages[0];
      final groupMessages = _getImageGroupMessages(messages, targetMessage);

      // Should only return the first image since it's from a different sender
      expect(groupMessages.length, equals(1));
      expect(groupMessages[0].id, equals('img1'));
    });

    test('Should not group images sent more than 1 minute apart', () {
      final now = DateTime.now();
      
      final messages = [
        Message(
          id: 'img1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image1.jpg',
          timestamp: now,
        ),
        Message(
          id: 'img2',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image2.jpg',
          timestamp: now.add(const Duration(minutes: 2)), // More than 1 minute apart
        ),
      ];

      final targetMessage = messages[0];
      final groupMessages = _getImageGroupMessages(messages, targetMessage);

      // Should only return the first image since the second is too far apart
      expect(groupMessages.length, equals(1));
      expect(groupMessages[0].id, equals('img1'));
    });
  });
}

// Helper function that mimics the logic from the chat screen
List<Message> _getImageGroupMessages(List<Message> messages, Message targetMessage) {
  if (targetMessage.type != MessageType.image) {
    return [targetMessage];
  }

  // Find messages that are part of the same image group
  // Images are grouped if they are from the same sender and sent within 1 minute
  final groupMessages = messages.where((msg) {
    return msg.type == MessageType.image &&
        msg.senderId == targetMessage.senderId &&
        msg.timestamp.difference(targetMessage.timestamp).inMinutes.abs() <= 1;
  }).toList();

  // Sort by timestamp to maintain order
  groupMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
  
  return groupMessages.isNotEmpty ? groupMessages : [targetMessage];
}
