import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mr_garments_mobile/screens/chat/member_chat_inbox.dart';
import 'package:mr_garments_mobile/services/session_service.dart';

class ChatFunctionality {
  /// Starts a chat with a user/entity (manufacturer, retailer, distributor)
  ///
  /// [context] - BuildContext for navigation and dialogs
  /// [entityData] - Map containing entity information (id, name, email, etc.)
  /// [entityType] - Type of entity ('manufacturer', 'retailer', 'distributor', 'user')
  static Future<void> startChatWithEntity(
    BuildContext context,
    Map<String, dynamic> entityData,
    String entityType,
  ) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // Create individual chat with this entity
      final chatId = await _createChatWithEntity(entityData, entityType);

      // Close loading dialog
      if (context.mounted) Navigator.pop(context);

      // Navigate to chat inbox
      if (context.mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => MemberChatInbox(
                  chatId: chatId,
                  chatName: entityData['name']?.toString() ?? 'Chat',
                  isGroup: false, // Individual chat
                ),
          ),
        );
      }
    } catch (e) {
      // Close loading dialog if still open
      if (context.mounted) Navigator.pop(context);

      // Show error
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error starting chat: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Creates a chat with an entity in Firestore
  static Future<String> _createChatWithEntity(
    Map<String, dynamic> entityData,
    String entityType,
  ) async {
    final currentUserId = await SessionService.getUserId();
    if (currentUserId == null) throw Exception('User not logged in');

    final otherUserId = entityData['id']?.toString() ?? '';
    if (otherUserId.isEmpty) throw Exception('Invalid entity ID');

    // Generate chat ID by combining user IDs
    final chatId = _generateChatId(currentUserId.toString(), otherUserId);

    // Check if chat already exists
    final existingChat =
        await FirebaseFirestore.instance.collection('chats').doc(chatId).get();

    if (existingChat.exists) {
      return chatId;
    }

    // Get current user info from session
    final currentUserName = await SessionService.getUserName() ?? 'Admin';
    final currentUserEmail = await SessionService.getUserEmail() ?? '';
    final currentUserRole = await SessionService.getUserRole() ?? 'admin';

    // Create chat users if they don't exist
    await _ensureChatUserExists(
      currentUserId.toString(),
      currentUserName,
      currentUserEmail,
      role: currentUserRole,
    );

    // Get entity name and email
    final entityName =
        entityData['name']?.toString() ??
        entityData['company_name']?.toString() ??
        entityData['contact_person_name']?.toString() ??
        'User';
    final entityEmail =
        entityData['email']?.toString() ??
        entityData['email_id']?.toString() ??
        '';

    await _ensureChatUserExists(
      otherUserId,
      entityName,
      entityEmail,
      role: entityType.toLowerCase(),
    );

    // Create new chat
    final now = DateTime.now();
    final chatData = {
      'id': chatId,
      'type': 'individual',
      'memberIds': [currentUserId.toString(), otherUserId],
      'memberNames': {
        currentUserId.toString(): currentUserName,
        otherUserId: entityName,
      },
      'memberProfileUrls': {currentUserId.toString(): '', otherUserId: ''},
      'isActive': true,
      'lastMessage': null,
      'lastMessageTime': null,
      'lastMessageSenderId': '',
      'unreadCounts': {currentUserId.toString(): 0, otherUserId: 0},
      'createdAt': now.millisecondsSinceEpoch,
      'updatedAt': now.millisecondsSinceEpoch,
    };

    await FirebaseFirestore.instance
        .collection('chats')
        .doc(chatId)
        .set(chatData);

    return chatId;
  }

  /// Ensures a chat user exists in Firestore
  static Future<void> _ensureChatUserExists(
    String userId,
    String name,
    String email, {
    String? role,
  }) async {
    final userDoc =
        await FirebaseFirestore.instance.collection('users').doc(userId).get();

    if (!userDoc.exists) {
      final now = DateTime.now();
      await FirebaseFirestore.instance.collection('users').doc(userId).set({
        'id': userId,
        'name': name,
        'email': email,
        'role': role ?? 'user', // Default role if not provided
        'profileImageUrl': '',
        'isOnline': false,
        'lastSeen': now.millisecondsSinceEpoch,
        'createdAt': now.millisecondsSinceEpoch,
        // Note: fcmToken is intentionally not set here - it will be set by FCM initialization
      });
    }
  }

  /// Generates a unique chat ID by combining two user IDs
  static String _generateChatId(String userId1, String userId2) {
    final sortedIds = [userId1, userId2]..sort();
    return '${sortedIds[0]}_${sortedIds[1]}';
  }
}
