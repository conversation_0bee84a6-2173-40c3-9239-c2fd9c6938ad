import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/distributor_service.dart';

// State class
class DistributorsState {
  final AsyncValue<List<dynamic>> distributors;
  final AsyncValue<Map<String, dynamic>> distributorDetails;

  DistributorsState({
    required this.distributors,
    required this.distributorDetails,
  });

  DistributorsState copyWith({
    AsyncValue<List<dynamic>>? distributors,
    AsyncValue<Map<String, dynamic>>? distributorDetails,
  }) {
    return DistributorsState(
      distributors: distributors ?? this.distributors,
      distributorDetails: distributorDetails ?? this.distributorDetails,
    );
  }
}

// Notifier
class DistributorsNotifier extends StateNotifier<DistributorsState> {
  DistributorsNotifier()
    : super(
        DistributorsState(
          distributors: const AsyncValue.loading(),
          distributorDetails: const AsyncValue.loading(),
        ),
      ) {
    fetchDistributors(); // Fetch on initialization
  }
  Future<void> fetchDistributors() async {
    try {
      final distributors = await DistributorService.fetchAllDistributors();
      state = state.copyWith(
        distributors: AsyncValue.data(distributors.reversed.toList()),
      );
    } catch (e, st) {
      state = state.copyWith(distributors: AsyncValue.error(e, st));
    }
  }

  Future<void> fetchDistributorDetails(int id) async {
    state = state.copyWith(distributorDetails: const AsyncValue.loading());
    try {
      final details = await DistributorService.fetchDistributorDetails(id);
      state = state.copyWith(distributorDetails: AsyncValue.data(details));
    } catch (e, st) {
      state = state.copyWith(distributorDetails: AsyncValue.error(e, st));
    }
  }

  Future<void> addDistributor(Map<String, dynamic> data) async {
    await DistributorService.addDistributor(data);
    await fetchDistributors();
  }

  Future<void> updateDistributor(int id, Map<String, dynamic> data) async {
    await DistributorService.updateDistributor(id, data);
    await fetchDistributors();
  }

  Future<Map<String, dynamic>> editDistributorDetails(int id) async {
    try {
      final details = await DistributorService.getDistributorDetails(id);
      state = state.copyWith(distributorDetails: AsyncValue.data(details));
      return details;
    } catch (e, st) {
      state = state.copyWith(distributorDetails: AsyncValue.error(e, st));
      rethrow;
    }
  }

  Future<void> updateDistributorStatus(int id, String status) async {
    try {
      await DistributorService.updateDistributorStatus(id, status);
      // Refresh details after update
      await fetchDistributors();
      await fetchDistributorDetails(id);
    } catch (e, st) {
      state = state.copyWith(distributorDetails: AsyncValue.error(e, st));
    }
  }
}

// Provider
final distributorsProvider =
    StateNotifierProvider<DistributorsNotifier, DistributorsState>((ref) {
      return DistributorsNotifier();
    });
