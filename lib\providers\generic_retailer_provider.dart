import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mr_garments_mobile/services/retailer_service.dart';

// Generic retailer state model
class GenericRetailerState {
  final AsyncValue<List<dynamic>> retailers;
  final AsyncValue<List<dynamic>> retailerRequests;

  const GenericRetailerState({
    required this.retailers,
    required this.retailerRequests,
  });

  GenericRetailerState copyWith({
    AsyncValue<List<dynamic>>? retailers,
    AsyncValue<List<dynamic>>? retailerRequests,
  }) {
    return GenericRetailerState(
      retailers: retailers ?? this.retailers,
      retailerRequests: retailerRequests ?? this.retailerRequests,
    );
  }
}

// Generic retailer notifier
class GenericRetailerNotifier extends StateNotifier<GenericRetailerState> {
  GenericRetailerNotifier()
    : super(
        const GenericRetailerState(
          retailers: AsyncLoading(),
          retailerRequests: AsyncLoading(),
        ),
      );

  /// Fetch retailers for a specific distributor
  Future<void> fetchRetailersByDistributor(int distributorId) async {
    try {
      state = state.copyWith(retailers: const AsyncLoading());
      final retailers = await RetailerService.fetchRetailersByDistributor(
        distributorId,
      );
      state = state.copyWith(retailers: AsyncData(retailers.reversed.toList()));
    } catch (e, st) {
      state = state.copyWith(retailers: AsyncError(e, st));
    }
  }

  /// Add a new retailer for a distributor
  Future<void> addRetailer({
    required String name,
    required String email,
    required String mobile,
    required String password,
    required String companyName,
    required String address,
    required int distributorId,
  }) async {
    await RetailerService.addRetailerForDistributor(
      name: name,
      email: email,
      mobile: mobile,
      password: password,
      companyName: companyName,
      address: address,
      distributorId: distributorId,
    );
    // Refresh retailer list after adding
    await fetchRetailersByDistributor(distributorId);
  }

  /// Update retailer
  Future<void> updateRetailer(
    int retailerId,
    Map<String, dynamic> retailerData,
    int distributorId,
  ) async {
    await RetailerService.updateRetailer(retailerId, retailerData);
    await fetchRetailersByDistributor(distributorId);
  }

  /// Deactivate retailer
  Future<void> deactivateRetailer(int retailerId, int distributorId) async {
    await RetailerService.deactivateRetailer(retailerId);
    await fetchRetailersByDistributor(distributorId);
  }

  /// Fetch retailer requests for admin approval
  Future<void> fetchRetailerRequests() async {
    try {
      state = state.copyWith(retailerRequests: const AsyncLoading());
      final requests = await RetailerService.fetchRetailerRequests().timeout(
        const Duration(seconds: 30),
      );
      state = state.copyWith(
        retailerRequests: AsyncData(requests.reversed.toList()),
      );
    } catch (e, st) {
      state = state.copyWith(retailerRequests: AsyncError(e, st));
    }
  }

  /// Refresh retailer requests without showing loading state
  Future<void> refreshRetailerRequestsSilently() async {
    try {
      final requests = await RetailerService.fetchRetailerRequests().timeout(
        const Duration(seconds: 30),
      );
      state = state.copyWith(
        retailerRequests: AsyncData(requests.reversed.toList()),
      );
    } catch (e) {
      print('Failed to refresh retailer requests: $e');
    }
  }

  /// Verify (approve/reject) retailer request
  Future<void> verifyRetailer(int retailerId, String action) async {
    try {
      // Optimistically remove the retailer request from the list immediately
      final currentRequests = state.retailerRequests;
      if (currentRequests is AsyncData) {
        final currentList = currentRequests.value;
        if (currentList != null) {
          final updatedRequests =
              currentList
                  .where((request) => request['id'] != retailerId)
                  .toList();
          state = state.copyWith(retailerRequests: AsyncData(updatedRequests));
        }
      }

      // Then verify the retailer
      await RetailerService.verifyRetailer(retailerId, action);

      // Finally refresh the retailer requests list to ensure consistency
      await refreshRetailerRequestsSilently();

      // If approved, also refresh all retailers lists for all distributors
      // This is a workaround since we don't know which distributor this retailer belongs to
      if (action == 'approve') {
        print('Retailer approved, should refresh distributor lists');
      }
    } catch (e) {
      // If verification fails, refresh the list to restore the correct state
      await refreshRetailerRequestsSilently();
      rethrow; // Re-throw the original error so the UI can handle it
    }
  }

  /// Clear retailer data (useful when switching companies)
  void clearRetailerData() {
    state = const GenericRetailerState(
      retailers: AsyncLoading(),
      retailerRequests: AsyncLoading(),
    );
  }
}

// Provider instances
final genericRetailerProvider =
    StateNotifierProvider<GenericRetailerNotifier, GenericRetailerState>((ref) {
      return GenericRetailerNotifier();
    });
