import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mr_garments_mobile/services/user_service.dart';

// Generic staff state model
class GenericStaffState {
  final AsyncValue<List<dynamic>> staffMembers;
  final AsyncValue<List<dynamic>> staffRequests;

  const GenericStaffState({
    required this.staffMembers,
    required this.staffRequests,
  });

  GenericStaffState copyWith({
    AsyncValue<List<dynamic>>? staffMembers,
    AsyncValue<List<dynamic>>? staffRequests,
  }) {
    return GenericStaffState(
      staffMembers: staffMembers ?? this.staffMembers,
      staffRequests: staffRequests ?? this.staffRequests,
    );
  }
}

// Generic staff notifier
class GenericStaffNotifier extends StateNotifier<GenericStaffState> {
  GenericStaffNotifier()
    : super(
        const GenericStaffState(
          staffMembers: AsyncLoading(),
          staffRequests: AsyncLoading(),
        ),
      );

  /// Fetch staff members for a specific company
  Future<void> fetchStaffByCompany(int companyId) async {
    try {
      state = state.copyWith(staffMembers: const AsyncLoading());
      final staff = await UserService.fetchStaffByCompany(companyId);
      state = state.copyWith(staffMembers: AsyncData(staff.reversed.toList()));
    } catch (e, st) {
      state = state.copyWith(staffMembers: AsyncError(e, st));
    }
  }

  /// Register a new staff member for any company type
  Future<void> registerStaff({
    required String name,
    required String email,
    required String mobile,
    required String password,
    required int companyId,
    required String companyType,
  }) async {
    await UserService.registerStaff(
      name: name,
      email: email,
      mobile: mobile,
      password: password,
      companyId: companyId,
      companyType: companyType,
    );
    // Refresh staff list and requests after registration
    await fetchStaffByCompany(companyId);
    await fetchStaffRequests();
  }

  /// Update staff member
  Future<void> updateStaff(
    int staffId,
    Map<String, dynamic> staffData,
    int companyId,
  ) async {
    await UserService.updateStaff(staffId, staffData);
    await fetchStaffByCompany(companyId);
  }

  /// Deactivate staff member
  Future<void> deactivateStaff(int staffId, int companyId) async {
    await UserService.deactivateStaff(staffId);
    await fetchStaffByCompany(companyId);
  }

  /// Fetch staff requests for admin approval
  Future<void> fetchStaffRequests() async {
    try {
      state = state.copyWith(staffRequests: const AsyncLoading());
      final requests = await UserService.fetchStaffRequests().timeout(
        const Duration(seconds: 30),
      ); // Add timeout
      state = state.copyWith(
        staffRequests: AsyncData(requests.reversed.toList()),
      );
    } catch (e, st) {
      state = state.copyWith(staffRequests: AsyncError(e, st));
    }
  }

  /// Refresh staff requests without showing loading state (for after verification)
  Future<void> refreshStaffRequestsSilently() async {
    try {
      final requests = await UserService.fetchStaffRequests().timeout(
        const Duration(seconds: 30),
      );
      state = state.copyWith(
        staffRequests: AsyncData(requests.reversed.toList()),
      );
    } catch (e) {
      // If refresh fails, keep the current state but log the error
      print('Failed to refresh staff requests: $e');
    }
  }

  /// Verify (approve/reject) staff member request
  Future<void> verifyStaff(int staffId, String action) async {
    try {
      // Optimistically remove the staff request from the list immediately
      final currentRequests = state.staffRequests;
      if (currentRequests is AsyncData) {
        final currentList = currentRequests.value;
        if (currentList != null) {
          final updatedRequests =
              currentList.where((request) => request['id'] != staffId).toList();
          state = state.copyWith(staffRequests: AsyncData(updatedRequests));
        }
      }

      // Then verify the staff member
      await UserService.verifyStaff(staffId, action);

      // Finally refresh the staff requests list to ensure consistency
      await refreshStaffRequestsSilently();
    } catch (e) {
      // If verification fails, refresh the list to restore the correct state
      await refreshStaffRequestsSilently();
      rethrow; // Re-throw the original error so the UI can handle it
    }
  }

  /// Clear staff data (useful when switching companies)
  void clearStaffData() {
    state = const GenericStaffState(
      staffMembers: AsyncLoading(),
      staffRequests: AsyncLoading(),
    );
  }
}

// Provider instances
final genericStaffProvider =
    StateNotifierProvider<GenericStaffNotifier, GenericStaffState>((ref) {
      return GenericStaffNotifier();
    });

// Convenience providers for specific data
final genericStaffMembersProvider = Provider<AsyncValue<List<dynamic>>>((ref) {
  return ref.watch(genericStaffProvider).staffMembers;
});

final genericStaffRequestsProvider = Provider<AsyncValue<List<dynamic>>>((ref) {
  return ref.watch(genericStaffProvider).staffRequests;
});
