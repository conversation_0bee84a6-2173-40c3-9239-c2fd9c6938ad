import 'package:flutter_test/flutter_test.dart';
import 'package:mr_garments_mobile/models/message.dart';

void main() {
  group('Multi-Message Actions Fix Tests', () {
    test('Should process all selected messages for sharing', () {
      final now = DateTime.now();
      
      // Create a list of selected messages including grouped images
      final selectedMessages = [
        // First group of images (3 images)
        Message(
          id: 'img1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image1.jpg',
          timestamp: now,
        ),
        Message(
          id: 'img2',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image2.jpg',
          timestamp: now.add(const Duration(seconds: 10)),
        ),
        Message(
          id: 'img3',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image3.jpg',
          timestamp: now.add(const Duration(seconds: 20)),
        ),
        // Text message
        Message(
          id: 'text1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.text,
          text: 'Hello world',
          timestamp: now.add(const Duration(minutes: 2)),
        ),
        // Second group of images (2 images)
        Message(
          id: 'img4',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image4.jpg',
          timestamp: now.add(const Duration(minutes: 3)),
        ),
        Message(
          id: 'img5',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image5.jpg',
          timestamp: now.add(const Duration(minutes: 3, seconds: 30)),
        ),
      ];

      // Test that all messages are processed
      expect(selectedMessages.length, equals(6));
      
      // Test image extraction for create catalog
      final imageUrls = selectedMessages
          .where((m) => m.type == MessageType.image && m.mediaUrl != null)
          .map((m) => m.mediaUrl!)
          .toList();
      
      expect(imageUrls.length, equals(5)); // Should extract all 5 images
      expect(imageUrls.contains('https://example.com/image1.jpg'), isTrue);
      expect(imageUrls.contains('https://example.com/image2.jpg'), isTrue);
      expect(imageUrls.contains('https://example.com/image3.jpg'), isTrue);
      expect(imageUrls.contains('https://example.com/image4.jpg'), isTrue);
      expect(imageUrls.contains('https://example.com/image5.jpg'), isTrue);
    });

    test('Should handle mixed message types correctly', () {
      final now = DateTime.now();
      
      final selectedMessages = [
        Message(
          id: 'text1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.text,
          text: 'Hello',
          timestamp: now,
        ),
        Message(
          id: 'img1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image1.jpg',
          timestamp: now.add(const Duration(seconds: 10)),
        ),
        Message(
          id: 'catalog1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.catalog,
          timestamp: now.add(const Duration(seconds: 20)),
        ),
      ];

      // Test that all message types are included
      expect(selectedMessages.length, equals(3));
      
      // Test filtering for different actions
      final textMessages = selectedMessages.where((m) => m.type == MessageType.text).toList();
      final imageMessages = selectedMessages.where((m) => m.type == MessageType.image).toList();
      final catalogMessages = selectedMessages.where((m) => m.type == MessageType.catalog).toList();
      
      expect(textMessages.length, equals(1));
      expect(imageMessages.length, equals(1));
      expect(catalogMessages.length, equals(1));
      
      // Test action availability logic
      final hasOnlyImages = selectedMessages.every((m) => m.type == MessageType.image);
      final hasOnlyCatalogs = selectedMessages.every((m) => m.type == MessageType.catalog);
      
      expect(hasOnlyImages, isFalse); // Mixed types, so not only images
      expect(hasOnlyCatalogs, isFalse); // Mixed types, so not only catalogs
    });

    test('Should correctly identify when only images are selected', () {
      final now = DateTime.now();
      
      final onlyImageMessages = [
        Message(
          id: 'img1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image1.jpg',
          timestamp: now,
        ),
        Message(
          id: 'img2',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image2.jpg',
          timestamp: now.add(const Duration(seconds: 10)),
        ),
      ];

      final hasOnlyImages = onlyImageMessages.every((m) => m.type == MessageType.image);
      expect(hasOnlyImages, isTrue); // Should enable "Create Catalog" action
      
      final imageUrls = onlyImageMessages
          .where((m) => m.type == MessageType.image && m.mediaUrl != null)
          .map((m) => m.mediaUrl!)
          .toList();
      
      expect(imageUrls.length, equals(2));
    });

    test('Should correctly identify when only catalogs are selected', () {
      final now = DateTime.now();
      
      final onlyCatalogMessages = [
        Message(
          id: 'catalog1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.catalog,
          timestamp: now,
        ),
        Message(
          id: 'catalog2',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.catalog,
          timestamp: now.add(const Duration(seconds: 10)),
        ),
      ];

      final hasOnlyCatalogs = onlyCatalogMessages.every((m) => m.type == MessageType.catalog);
      expect(hasOnlyCatalogs, isTrue); // Should enable "Create Order" action
      
      final catalogMessages = onlyCatalogMessages
          .where((m) => m.type == MessageType.catalog)
          .toList();
      
      expect(catalogMessages.length, equals(2));
    });
  });
}
