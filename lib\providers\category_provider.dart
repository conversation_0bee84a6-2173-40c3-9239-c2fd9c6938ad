import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mr_garments_mobile/services/category_service.dart';

/// --- Category State Class ---
class CategoryState {
  final AsyncValue<List<Map<String, dynamic>>> categories;

  const CategoryState({required this.categories});

  CategoryState copyWith({AsyncValue<List<Map<String, dynamic>>>? categories}) {
    return CategoryState(categories: categories ?? this.categories);
  }
}

/// --- Category Notifier ---
class CategoryNotifier extends StateNotifier<CategoryState> {
  CategoryNotifier()
    : super(const CategoryState(categories: AsyncValue.loading())) {
    fetchCategories();
  }

  Future<void> fetchCategories() async {
    try {
      final data = await CategoryService.fetchCategories();
      state = state.copyWith(
        categories: AsyncValue.data(data.reversed.toList()),
      );
    } catch (e, st) {
      state = state.copyWith(
        categories: AsyncValue.error(
          Exception('Failed to load categories'),
          st,
        ),
      );
    }
  }

  Future<void> addCategoryAndRefresh(Map<String, dynamic> categoryData) async {
    await CategoryService.addCategory(categoryData);
    await fetchCategories();
  }
}

/// --- Providers ---
final categoryProvider = StateNotifierProvider<CategoryNotifier, CategoryState>(
  (ref) => CategoryNotifier(),
);

/// Selected image file used when uploading a category image
final selectedCategoryImageProvider = StateProvider<File?>((ref) => null);

final fetchCategoryDetailsProvider =
    FutureProvider.family<Map<String, dynamic>, int>((ref, id) async {
      return await CategoryService.fetchCategoryDetails(id);
    });

// Add this provider for category catalogs
final categoryCatalogsProvider =
    FutureProvider.family<List<Map<String, dynamic>>, int>((
      ref,
      categoryId,
    ) async {
      return await CategoryService.fetchCatalogsByCategory(categoryId);
    });
