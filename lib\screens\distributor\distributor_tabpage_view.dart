import 'dart:async';
 
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/screens/admin/admin_home_page.dart';
import 'package:mr_garments_mobile/screens/distributor/add_edit_distributor.dart';
import 'package:mr_garments_mobile/screens/distributor/distributor_userlist.dart';

class DistributorTabpageView extends StatefulWidget {
  const DistributorTabpageView({super.key});

  @override
  State<DistributorTabpageView> createState() => _DistributorTabpageViewState();
}

class _DistributorTabpageViewState extends State<DistributorTabpageView> {
  Timer? _debounce;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      if (_debounce?.isActive ?? false) _debounce!.cancel();
      _debounce = Timer(const Duration(milliseconds: 300), () {
        setState(() {}); // refresh after user stops typing for 300ms
      });
    });
  }

  Widget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xFF005368),
      foregroundColor: Colors.white,
      elevation: 0,
      title: Row(
        children: [
          Text(
            "Distributor",
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () {
              Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(builder: (context) => AdminHomePage()),
                (route) => false,
              );
            },
            child: const Icon(Icons.home, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndAddDistributor() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: "Search Distributors...",
                prefixIcon: const Icon(Icons.search),
                filled: true,
                fillColor: Color.fromARGB(248, 240, 240, 240),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(30),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),
          const SizedBox(width: 10),
          IconButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFFF2A738),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(40),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 10),
            ),
            onPressed: () {
              // Navigate to Add distributor screen
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => AddEditDistributor()),
              );
            },
            icon: const Icon(Icons.add),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: _buildAppBar(),
      ),
      body: Column(
        children: [
          _buildSearchAndAddDistributor(),
          Expanded(child: DistributorUserlist(searchQuery: _searchController.text)),
        ],
      ),
    );
  }
}
