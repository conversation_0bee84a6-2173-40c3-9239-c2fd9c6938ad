import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/screens/widgets/brands_section.dart';
import 'package:mr_garments_mobile/services/brand_service.dart';

class BrandsViewallScreen extends StatefulWidget {
  const BrandsViewallScreen({super.key});

  @override
  State<BrandsViewallScreen> createState() => _BrandsViewallScreenState();
}

class _BrandsViewallScreenState extends State<BrandsViewallScreen> {
  List<String> brandImages = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchBrands();
  }

  Future<void> _fetchBrands() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      final brands = await BrandService.fetchBrands();
      brandImages = brands
          .map<String>((b) => (b['image'] is List && b['image'].isNotEmpty) ? b['image'][0] as String : '')
          .where((url) => url.isNotEmpty)
          .toList();
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('All Brands'),
        foregroundColor: Colors.white,
        backgroundColor: const Color(0xFF005368),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(14),
                  child: BrandsSection(
                    brandImages: brandImages,
                    maxItems: null, // Show all brands
                  ),
                ),
    );
  }
}