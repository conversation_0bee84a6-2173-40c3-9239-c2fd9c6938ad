# Backend API Requirements for Profile Management

## Overview
This document outlines the required backend APIs for the Mr. Garments mobile app profile management functionality.

## Current User Data Structure
Based on the existing login API response, the user object contains:
```json
{
  "id": 1,
  "name": "Super Admin",
  "email": "<EMAIL>",
  "username": "admin",
  "email_verified_at": "2025-06-20T16:28:43.000000Z",
  "created_at": "2025-06-20T16:28:43.000000Z",
  "updated_at": "2025-06-20T11:01:43.000000Z",
  "mobile_number": null,
  "address": null,
  "job_role": null,
  "account_type": "Admin",
  "status": null,
  "brands": null,
  "license_details": null,
  "tally_manufacturer_id": null,
  "contact_person": null,
  "agree_terms": 0,
  "company_name": null,
  "profile_image_url": null  // NEW FIELD NEEDED
}
```

## Required API Endpoints

### 1. Get Current User Profile
**Endpoint:** `GET /api/profile`
**Headers:** `Authorization: Bearer {token}`
**Response:**
```json
{
  "message": "Profile retrieved successfully",
  "user": {
    // Complete user object with all fields
  }
}
```

### 2. Update User Profile
**Endpoint:** `PUT /api/profile`
**Headers:** 
- `Authorization: Bearer {token}`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "name": "Updated Name",
  "email": "<EMAIL>",
  "mobile_number": "**********",  // Optional
  "address": "New Address",       // Optional
  "profile_image_url": "https://example.com/image.jpg"  // Optional
}
```

**Response:**
```json
{
  "message": "Profile updated successfully",
  "user": {
    // Updated user object
  }
}
```

**Validation Rules:**
- `name`: Required, string, max 255 characters
- `email`: Required, valid email format, unique in database
- `mobile_number`: Optional, string, valid phone number format
- `address`: Optional, string, max 500 characters
- `profile_image_url`: Optional, valid URL format

### 3. Upload Profile Image
**Endpoint:** `POST /api/profile/upload-image`
**Headers:** 
- `Authorization: Bearer {token}`
- `Content-Type: multipart/form-data`

**Request Body:**
- `image`: File (jpg, jpeg, png, max 5MB)

**Response:**
```json
{
  "message": "Image uploaded successfully",
  "image_url": "https://mrgarment.braincavesoft.com/storage/profile_images/user_1_profile.jpg"
}
```

**Image Processing Requirements:**
- Resize to maximum 512x512 pixels
- Compress to reduce file size
- Store in `storage/profile_images/` directory
- Generate unique filename: `user_{user_id}_profile_{timestamp}.{extension}`

### 4. Change Password
**Endpoint:** `PUT /api/profile/change-password`
**Headers:** 
- `Authorization: Bearer {token}`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "current_password": "oldpassword",
  "new_password": "newpassword",
  "new_password_confirmation": "newpassword"
}
```

**Response:**
```json
{
  "message": "Password changed successfully"
}
```

**Validation Rules:**
- `current_password`: Required, must match user's current password
- `new_password`: Required, minimum 6 characters
- `new_password_confirmation`: Required, must match `new_password`

## Database Schema Updates

### Add Profile Image Column
```sql
ALTER TABLE users ADD COLUMN profile_image_url VARCHAR(255) NULL AFTER company_name;
```

## Error Handling
All endpoints should return appropriate HTTP status codes and error messages:

**400 Bad Request:**
```json
{
  "message": "Validation failed",
  "errors": {
    "email": ["The email field is required."],
    "name": ["The name field is required."]
  }
}
```

**401 Unauthorized:**
```json
{
  "message": "Unauthenticated"
}
```

**422 Unprocessable Entity:**
```json
{
  "message": "The given data was invalid",
  "errors": {
    "current_password": ["The current password is incorrect."]
  }
}
```

**500 Internal Server Error:**
```json
{
  "message": "Internal server error"
}
```

## Security Considerations
1. Validate file types and sizes for image uploads
2. Sanitize file names to prevent directory traversal
3. Verify current password before allowing password changes
4. Rate limit password change attempts
5. Log profile update activities for audit purposes

## Implementation Priority
1. **High Priority:** Update Profile API (without image)
2. **High Priority:** Change Password API
3. **Medium Priority:** Upload Profile Image API
4. **Low Priority:** Get Current Profile API (can use existing session data)

## Testing Checklist
- [ ] Profile update with all fields
- [ ] Profile update with partial fields
- [ ] Email uniqueness validation
- [ ] Password change with correct current password
- [ ] Password change with incorrect current password
- [ ] Image upload with valid formats
- [ ] Image upload with invalid formats/sizes
- [ ] Unauthorized access attempts
