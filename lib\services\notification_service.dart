import 'dart:convert';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/screens/chat/member_chat_inbox.dart';

/// Global function to handle background messages
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await NotificationService.handleBackgroundMessage(message);
}

class NotificationService {
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  static bool _isInitialized = false;
  static String? _currentUserId;

  // Notification channels
  static const String _chatChannelId = 'mr_garments_chat';
  static const String _chatChannelName = 'Chat Messages';
  static const String _chatChannelDescription =
      'Notifications for new chat messages';

  /// Initialize notification service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _currentUserId = (await SessionService.getUserId())?.toString();
      debugPrint('🔔 Initializing notifications for user: $_currentUserId');

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Initialize Firebase messaging
      await _initializeFirebaseMessaging();

      _isInitialized = true;
      debugPrint('✅ Notification service initialized successfully');
    } catch (e) {
      debugPrint('❌ Failed to initialize notification service: $e');
    }
  }

  /// Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    // Android initialization settings
    const AndroidInitializationSettings androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // iOS initialization settings
    const DarwinInitializationSettings iosSettings =
        DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        );

    const InitializationSettings initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channel for Android
    if (Platform.isAndroid) {
      await _createNotificationChannel();
    }
  }

  /// Create notification channel for Android
  static Future<void> _createNotificationChannel() async {
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      _chatChannelId,
      _chatChannelName,
      description: _chatChannelDescription,
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound('notification'),
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(channel);
  }

  /// Initialize Firebase messaging
  static Future<void> _initializeFirebaseMessaging() async {
    // Request permissions
    final settings = await _messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      // Get and update FCM token
      await _updateFCMToken();

      // Listen for token refresh
      _messaging.onTokenRefresh.listen(_onTokenRefresh);

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // Handle background message taps
      FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

      // Handle app launch from terminated state
      _handleAppLaunchFromNotification();
    }
  }

  /// Update FCM token
  static Future<void> _updateFCMToken() async {
    try {
      final token = await _messaging.getToken();
      debugPrint('🔑 Got FCM token: ${token?.substring(0, 20)}...');
      debugPrint('👤 Current user ID: $_currentUserId');

      if (token != null && _currentUserId != null) {
        await ChatService.updateFCMToken(_currentUserId!, token);
        debugPrint('✅ FCM token updated for user $_currentUserId');
      } else {
        debugPrint(
          '❌ Cannot update FCM token - token: ${token != null}, userId: ${_currentUserId != null}',
        );
      }
    } catch (e) {
      debugPrint('❌ Failed to update FCM token: $e');
    }
  }

  /// Handle token refresh
  static Future<void> _onTokenRefresh(String newToken) async {
    try {
      debugPrint('🔄 FCM token refreshed: ${newToken.substring(0, 20)}...');
      if (_currentUserId != null) {
        await ChatService.updateFCMToken(_currentUserId!, newToken);
        debugPrint('✅ Refreshed FCM token saved for user $_currentUserId');
      } else {
        debugPrint('❌ Cannot save refreshed FCM token - no current user ID');
      }
    } catch (e) {
      debugPrint('❌ Failed to update refreshed FCM token: $e');
    }
  }

  /// Handle foreground messages
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    // Don't show notification if user is currently in the chat screen
    if (_isInChatScreen(message.data['chatId'])) {
      return;
    }

    await showLocalNotification(message);
  }

  /// Handle background messages
  static Future<void> handleBackgroundMessage(RemoteMessage message) async {
    // Background messages are automatically displayed by the system
    // We just need to handle any data processing here if needed
    debugPrint('Background message received: ${message.messageId}');
  }

  /// Handle message opened app (from background)
  static Future<void> _handleMessageOpenedApp(RemoteMessage message) async {
    await _navigateToChat(message.data);
  }

  /// Handle app launch from terminated state
  static Future<void> _handleAppLaunchFromNotification() async {
    final initialMessage = await _messaging.getInitialMessage();
    if (initialMessage != null) {
      await _navigateToChat(initialMessage.data);
    }
  }

  /// Show local notification
  static Future<void> showLocalNotification(RemoteMessage message) async {
    final data = message.data;
    final notification = message.notification;

    if (notification == null) return;

    const AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
          _chatChannelId,
          _chatChannelName,
          channelDescription: _chatChannelDescription,
          importance: Importance.high,
          priority: Priority.high,
          icon: '@mipmap/ic_launcher',
          sound: RawResourceAndroidNotificationSound('notification'),
          enableVibration: true,
          playSound: true,
        );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      sound: 'default',
    );

    const NotificationDetails notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      data['chatId'].hashCode, // Use chatId hash as notification ID
      notification.title ?? 'MR Garments',
      notification.body ?? 'New message',
      notificationDetails,
      payload: jsonEncode(data),
    );
  }

  /// Handle notification tap
  static Future<void> _onNotificationTapped(
    NotificationResponse response,
  ) async {
    if (response.payload != null) {
      final data = jsonDecode(response.payload!);
      await _navigateToChat(data);
    }
  }

  /// Navigate to chat screen
  static Future<void> _navigateToChat(Map<String, dynamic> data) async {
    final chatId = data['chatId'];
    final chatName = data['otherUserName'] ?? data['chatName'] ?? 'Chat';
    final isGroup = data['isGroup'] == 'true';

    if (chatId != null) {
      // Get the current context from the navigator
      final context = NavigatorService.navigatorKey.currentContext;
      if (context != null) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => MemberChatInbox(
                  chatId: chatId,
                  chatName: chatName,
                  isGroup: isGroup,
                ),
          ),
        );
      }
    }
  }

  /// Check if user is currently in the specific chat screen
  static bool _isInChatScreen(String? chatId) {
    // This would need to be implemented based on your navigation state management
    // For now, return false to always show notifications
    return false;
  }

  /// Clear all notifications
  static Future<void> clearAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  /// Clear notifications for specific chat
  static Future<void> clearChatNotifications(String chatId) async {
    await _localNotifications.cancel(chatId.hashCode);
  }

  /// Update user ID when user logs in/out
  static Future<void> updateUserId(String? userId) async {
    _currentUserId = userId;
    if (userId != null) {
      await _updateFCMToken();
    }
  }

  /// Dispose notification service
  static Future<void> dispose() async {
    _isInitialized = false;
    _currentUserId = null;
  }
}

/// Navigator service to maintain global navigator key
class NavigatorService {
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();
}
