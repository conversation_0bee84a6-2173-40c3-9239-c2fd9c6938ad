// lib/services/credential_service.dart
import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class CredentialService {
  // Keys for secure storage
  static const String _credentialsKey = 'saved_credentials';
  static const String _rememberMeKey = 'remember_me_enabled';
  
  // Secure storage instance with encryption options
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
    wOptions: WindowsOptions(),
    lOptions: LinuxOptions(),
    webOptions: WebOptions(),
  );

  /// Save user credentials securely when Remember Me is enabled
  static Future<void> saveCredentials({
    required String email,
    required String password,
  }) async {
    try {
      // Create credentials map - store password as plain text for auto-fill
      // In production, consider using symmetric encryption instead of hash
      final credentials = {
        'email': email,
        'password': password, // Store plain text for auto-fill functionality
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      // Save credentials as JSON string
      await _secureStorage.write(
        key: _credentialsKey,
        value: json<PERSON>ncode(credentials),
      );

      // Mark remember me as enabled
      await _secureStorage.write(
        key: _rememberMeKey,
        value: 'true',
      );
    } catch (e) {
      // Silently handle storage errors
      throw Exception('Failed to save credentials: $e');
    }
  }

  /// Retrieve saved credentials
  static Future<Map<String, String>?> getSavedCredentials() async {
    try {
      final credentialsJson = await _secureStorage.read(key: _credentialsKey);
      final rememberMeEnabled = await _secureStorage.read(key: _rememberMeKey);

      if (credentialsJson != null && rememberMeEnabled == 'true') {
        final credentials = jsonDecode(credentialsJson) as Map<String, dynamic>;
        
        // Check if credentials are not too old (optional: 30 days expiry)
        final timestamp = credentials['timestamp'] as int?;
        if (timestamp != null) {
          final savedDate = DateTime.fromMillisecondsSinceEpoch(timestamp);
          final daysDifference = DateTime.now().difference(savedDate).inDays;
          
          // If credentials are older than 30 days, clear them
          if (daysDifference > 30) {
            await clearCredentials();
            return null;
          }
        }

        return {
          'email': credentials['email'] as String,
          'password': credentials['password'] as String,
        };
      }
      return null;
    } catch (e) {
      // If there's an error reading credentials, clear them
      await clearCredentials();
      return null;
    }
  }

  /// Check if Remember Me is currently enabled
  static Future<bool> isRememberMeEnabled() async {
    try {
      final rememberMeEnabled = await _secureStorage.read(key: _rememberMeKey);
      return rememberMeEnabled == 'true';
    } catch (e) {
      return false;
    }
  }

  /// Clear saved credentials and disable Remember Me
  static Future<void> clearCredentials() async {
    try {
      await _secureStorage.delete(key: _credentialsKey);
      await _secureStorage.delete(key: _rememberMeKey);
    } catch (e) {
      // Silently handle deletion errors
    }
  }

  /// Update Remember Me preference without affecting saved credentials
  static Future<void> setRememberMeEnabled(bool enabled) async {
    try {
      if (enabled) {
        await _secureStorage.write(key: _rememberMeKey, value: 'true');
      } else {
        // If disabling Remember Me, clear all saved credentials
        await clearCredentials();
      }
    } catch (e) {
      // Silently handle storage errors
    }
  }

  /// Clear all stored data (for logout or reset)
  static Future<void> clearAllStoredData() async {
    try {
      await _secureStorage.deleteAll();
    } catch (e) {
      // Silently handle deletion errors
    }
  }
}
