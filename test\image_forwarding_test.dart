import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/screens/chat/widgets/image_viewer_screen.dart';
import 'package:mr_garments_mobile/models/message.dart';

void main() {
  group('Image Forwarding Tests', () {
    testWidgets('ImageViewerScreen should display images correctly', (
      WidgetTester tester,
    ) async {
      // Create test data
      final testImageUrls = [
        'https://example.com/image1.jpg',
        'https://example.com/image2.jpg',
        'https://example.com/image3.jpg',
      ];

      final testMessages =
          testImageUrls
              .map(
                (url) => Message(
                  id: 'test_${url.hashCode}',
                  senderId: 'test_sender',
                  senderName: 'Test Sender',
                  type: MessageType.image,
                  mediaUrl: url,
                  timestamp: DateTime.now(),
                ),
              )
              .toList();

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: ImageViewerScreen(
            imageUrls: testImageUrls,
            imageMessages: testMessages,
            initialIndex: 0,
            senderName: 'Test Sender',
            timestamp: '12:00 PM',
            chatId: 'test_chat_id',
          ),
        ),
      );

      // Verify the widget is displayed
      expect(find.byType(ImageViewerScreen), findsOneWidget);
    });

    testWidgets('Grid view should support selection mode', (
      WidgetTester tester,
    ) async {
      final testImageUrls = [
        'https://example.com/image1.jpg',
        'https://example.com/image2.jpg',
      ];

      final testMessages =
          testImageUrls
              .map(
                (url) => Message(
                  id: 'test_${url.hashCode}',
                  senderId: 'test_sender',
                  senderName: 'Test Sender',
                  type: MessageType.image,
                  mediaUrl: url,
                  timestamp: DateTime.now(),
                ),
              )
              .toList();

      await tester.pumpWidget(
        MaterialApp(
          home: ImageViewerScreen(
            imageUrls: testImageUrls,
            imageMessages: testMessages,
            initialIndex: 0,
            senderName: 'Test Sender',
            timestamp: '12:00 PM',
            chatId: 'test_chat_id',
          ),
        ),
      );

      // Switch to grid view
      await tester.tap(find.byIcon(LucideIcons.grid));
      await tester.pumpAndSettle();

      // The grid view should be displayed
      expect(find.byType(GridView), findsOneWidget);
    });

    testWidgets('Clicking specific image in grid should open that image', (
      WidgetTester tester,
    ) async {
      final testImageUrls = [
        'https://example.com/image1.jpg',
        'https://example.com/image2.jpg',
        'https://example.com/image3.jpg',
      ];

      final testMessages =
          testImageUrls
              .map(
                (url) => Message(
                  id: 'test_${url.hashCode}',
                  senderId: 'test_sender',
                  senderName: 'Test Sender',
                  type: MessageType.image,
                  mediaUrl: url,
                  timestamp: DateTime.now(),
                ),
              )
              .toList();

      await tester.pumpWidget(
        MaterialApp(
          home: ImageViewerScreen(
            imageUrls: testImageUrls,
            imageMessages: testMessages,
            initialIndex: 0,
            senderName: 'Test Sender',
            timestamp: '12:00 PM',
            chatId: 'test_chat_id',
          ),
        ),
      );

      // Switch to grid view
      await tester.tap(find.byIcon(LucideIcons.grid));
      await tester.pumpAndSettle();

      // Tap on the second image (index 1)
      final gridItems = find.byType(GestureDetector);
      expect(gridItems, findsAtLeastNWidgets(3));

      // This test verifies the structure is correct
      // In a real test environment, you would verify the PageController's page
    });
  });

  group('Message Model Tests', () {
    test('Message should serialize and deserialize correctly', () {
      final originalMessage = Message(
        id: 'test_id',
        senderId: 'sender_123',
        senderName: 'John Doe',
        type: MessageType.image,
        mediaUrl: 'https://example.com/image.jpg',
        timestamp: DateTime(2024, 1, 1, 12, 0, 0),
        isForwarded: false,
      );

      // Convert to map and back
      final map = originalMessage.toMap();
      final deserializedMessage = Message.fromMap(map);

      // Verify all fields are preserved
      expect(deserializedMessage.id, equals(originalMessage.id));
      expect(deserializedMessage.senderId, equals(originalMessage.senderId));
      expect(
        deserializedMessage.senderName,
        equals(originalMessage.senderName),
      );
      expect(deserializedMessage.type, equals(originalMessage.type));
      expect(deserializedMessage.mediaUrl, equals(originalMessage.mediaUrl));
      expect(
        deserializedMessage.isForwarded,
        equals(originalMessage.isForwarded),
      );
    });
  });
}
