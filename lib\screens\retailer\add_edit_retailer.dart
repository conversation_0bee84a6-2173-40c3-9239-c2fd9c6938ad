import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/providers/retailer_provider.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class AddEditRetailer extends ConsumerStatefulWidget {
  final int? retailerId;
  const AddEditRetailer({super.key, this.retailerId});

  @override
  ConsumerState<AddEditRetailer> createState() => _AddEditRetailerState();
}

class _AddEditRetailerState extends ConsumerState<AddEditRetailer> {
  final _formKey = GlobalKey<FormState>();

  final _contactPersonController = TextEditingController();
  final _mobileController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();

  final List<String> _tallyRetailers = [
    'Tally Retailer A',
    'Tally Retailer B',
    'Tally Retailer C',
  ];
  String? _selectedTallyRetailer;
  bool _obscurePassword = true;
  bool _isPasswordChanged = false;
  bool _isLoading = false;
  bool get isEditMode => widget.retailerId != null;

  @override
  void initState() {
    super.initState();
    if (isEditMode) _loadRetailerData();
  }

  void _loadRetailerData() async {
    final data = await ref
        .read(retailersProvider.notifier)
        .editRetailerDetails(widget.retailerId!);
    setState(() {
      _contactPersonController.text = data['contactPerson'] ?? '';
      _mobileController.text = data['mobile'] ?? '';
      _emailController.text = data['email'] ?? '';
      _addressController.text = data['address'] ?? '';
      _usernameController.text = data['username'] ?? '';
      _passwordController.text = ''; // password not sent back from API
    });
  }

  @override
  void dispose() {
    _contactPersonController.dispose();
    _mobileController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  InputDecoration getDecoration(String label) => InputDecoration(
    labelText: label,
    filled: true,
    fillColor: Colors.grey.shade100,
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12),
      borderSide: BorderSide.none,
    ),
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: const Color(0xFF005368),
        foregroundColor: Colors.white,
        title: Text(
          isEditMode ? 'Edit Retailer' : 'Add Retailer',
          style: GoogleFonts.poppins(color: Colors.white, fontSize: 18),
        ),
      ),

      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          if (!isEditMode) ...[
                            const SizedBox(height: 10),
                            DropdownButtonFormField<String>(
                              value: _selectedTallyRetailer,
                              items:
                                  _tallyRetailers
                                      .map(
                                        (name) => DropdownMenuItem(
                                          value: name,
                                          child: Text(name),
                                        ),
                                      )
                                      .toList(),
                              onChanged: (value) {
                                setState(() => _selectedTallyRetailer = value);
                              },
                              decoration: getDecoration(
                                'Select Retailer from Tally',
                              ),
                              validator:
                                  (value) =>
                                      value == null
                                          ? 'Please select a retailer'
                                          : null,
                            ),
                          ],

                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _contactPersonController,
                            decoration: getDecoration('Contact Person Name'),
                            validator:
                                (value) =>
                                    value == null || value.isEmpty
                                        ? 'Enter contact person name'
                                        : null,
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _mobileController,
                            keyboardType: TextInputType.phone,
                            decoration: getDecoration('Mobile No.'),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Enter mobile number';
                              }
                              if (!RegExp(r'^\d{10}$').hasMatch(value)) {
                                return 'Enter valid 10-digit number';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _emailController,
                            keyboardType: TextInputType.emailAddress,
                            decoration: getDecoration('Email ID'),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Enter email';
                              }
                              if (!RegExp(
                                r'^[^@]+@[^@]+\.[^@]+$',
                              ).hasMatch(value)) {
                                return 'Enter valid email';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _addressController,
                            decoration: getDecoration('Address'),
                            validator:
                                (value) =>
                                    value == null || value.isEmpty
                                        ? 'Please enter address'
                                        : null,
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _usernameController,
                            decoration: getDecoration('Username'),
                            validator:
                                (value) =>
                                    value == null || value.isEmpty
                                        ? 'Enter username'
                                        : null,
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _passwordController,

                            decoration: getDecoration('Password').copyWith(
                              suffixIcon: IconButton(
                                onPressed: () {
                                  setState(() {
                                    _obscurePassword = !_obscurePassword;
                                  });
                                },
                                icon: Icon(
                                  _obscurePassword
                                      ? Icons.visibility_off
                                      : Icons.visibility,
                                ),
                              ),
                            ),
                            obscureText: _obscurePassword,
                            validator: (v) {
                              if (!isEditMode && (v == null || v.length < 6)) {
                                return 'Min. 6 characters required';
                              }
                              return null;
                            },
                            onChanged: (_) {
                              if (isEditMode) {
                                setState(() {
                                  _isPasswordChanged = true;
                                });
                              }
                            },
                          ),
                          const SizedBox(height: 43),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              icon: const Icon(Icons.save),
                              label: Text(
                                isEditMode
                                    ? "Update Retailer"
                                    : "Create Retailer",
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFFF2A738),
                                padding: const EdgeInsets.symmetric(
                                  vertical: 14,
                                ),
                              ),
                              onPressed: () async {
                                if (_formKey.currentState!.validate()) {
                                  setState(() => _isLoading = true);

                                  final data = {
                                    'contactPerson':
                                        _contactPersonController.text.trim(),
                                    'mobile': _mobileController.text.trim(),
                                    'email': _emailController.text.trim(),
                                    'address': _addressController.text..trim(),
                                    'username': _usernameController.text.trim(),
                                  };
                                  if (!isEditMode || _isPasswordChanged) {
                                    data["password"] =
                                        _passwordController.text.trim();
                                  }

                                  final notifier = ref.read(
                                    retailersProvider.notifier,
                                  );
                                  try {
                                    if (isEditMode) {
                                      await notifier.updateRetailer(
                                        widget.retailerId!,
                                        data,
                                      );
                                    } else {
                                      data["tallyRetailerId"] =
                                          _selectedTallyRetailer!;
                                      await notifier.addRetailer(data);
                                    }
                                    if (!context.mounted) return;
                                    Navigator.pop(context);
                                  } catch (e) {
                                    if (context.mounted) {
                                      AppSnackbar.showError(
                                        context,
                                        "Error: $e",
                                      );
                                    }
                                  } finally {
                                    if (mounted) {
                                      setState(
                                        () => _isLoading = false,
                                      ); // hide loader
                                    }
                                  }

                                  if (context.mounted) {
                                    AppSnackbar.showSuccess(
                                      context,
                                      'Retailer created successfully!',
                                    );
                                  }
                                  // Submit logic here
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (_isLoading)
                    Container(
                      color: Colors.black.withAlpha(77),
                      child: const Center(child: CircularProgressIndicator()),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
