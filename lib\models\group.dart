import 'package:cloud_firestore/cloud_firestore.dart';

class GroupMember {
  final String userId;
  final String name;
  final String? profileUrl;
  final String role; // admin, member
  final DateTime joinedAt;
  final bool isActive;

  GroupMember({
    required this.userId,
    required this.name,
    this.profileUrl,
    this.role = 'member',
    required this.joinedAt,
    this.isActive = true,
  });

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'name': name,
      'profileUrl': profileUrl,
      'role': role,
      'joinedAt': joinedAt.millisecondsSinceEpoch,
      'isActive': isActive,
    };
  }

  factory GroupMember.fromMap(Map<String, dynamic> map) {
    return GroupMember(
      userId: map['userId'] ?? '',
      name: map['name'] ?? '',
      profileUrl: map['profileUrl'],
      role: map['role'] ?? 'member',
      joinedAt: DateTime.fromMillisecondsSinceEpoch(map['joinedAt']),
      isActive: map['isActive'] ?? true,
    );
  }

  GroupMember copyWith({
    String? userId,
    String? name,
    String? profileUrl,
    String? role,
    DateTime? joinedAt,
    bool? isActive,
  }) {
    return GroupMember(
      userId: userId ?? this.userId,
      name: name ?? this.name,
      profileUrl: profileUrl ?? this.profileUrl,
      role: role ?? this.role,
      joinedAt: joinedAt ?? this.joinedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GroupMember && other.userId == userId;
  }

  @override
  int get hashCode => userId.hashCode;
}

class Group {
  final String id;
  final String name;
  final String? description;
  final String? imageUrl;
  final String createdBy;
  final List<GroupMember> members;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  final Map<String, dynamic>? settings; // Group settings like mute, etc.

  Group({
    required this.id,
    required this.name,
    this.description,
    this.imageUrl,
    required this.createdBy,
    this.members = const [],
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.settings,
  });

  // Convert Group to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'createdBy': createdBy,
      'members': members.map((member) => member.toMap()).toList(),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'isActive': isActive,
      'settings': settings,
    };
  }

  // Create Group from Firestore document
  factory Group.fromMap(Map<String, dynamic> map) {
    return Group(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'],
      imageUrl: map['imageUrl'],
      createdBy: map['createdBy'] ?? '',
      members: (map['members'] as List<dynamic>?)
          ?.map((memberMap) => GroupMember.fromMap(memberMap))
          .toList() ?? [],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
      isActive: map['isActive'] ?? true,
      settings: map['settings'],
    );
  }

  // Create Group from Firestore DocumentSnapshot
  factory Group.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Group.fromMap(data);
  }

  // Copy with method for updating group data
  Group copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    String? createdBy,
    List<GroupMember>? members,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    Map<String, dynamic>? settings,
  }) {
    return Group(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      createdBy: createdBy ?? this.createdBy,
      members: members ?? this.members,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      settings: settings ?? this.settings,
    );
  }

  // Get member by user ID
  GroupMember? getMember(String userId) {
    try {
      return members.firstWhere((member) => member.userId == userId);
    } catch (e) {
      return null;
    }
  }

  // Check if user is admin
  bool isAdmin(String userId) {
    final member = getMember(userId);
    return member?.role == 'admin' || createdBy == userId;
  }

  // Check if user is member
  bool isMember(String userId) {
    return members.any((member) => member.userId == userId && member.isActive);
  }

  // Get active members count
  int get activeMembersCount {
    return members.where((member) => member.isActive).length;
  }

  // Get admin members
  List<GroupMember> get admins {
    return members.where((member) => 
        member.isActive && (member.role == 'admin' || member.userId == createdBy)
    ).toList();
  }

  // Get member IDs
  List<String> get memberIds {
    return members.where((member) => member.isActive)
        .map((member) => member.userId).toList();
  }

  @override
  String toString() {
    return 'Group(id: $id, name: $name, membersCount: ${members.length}, createdBy: $createdBy)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Group && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
