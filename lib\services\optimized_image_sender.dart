import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/services/optimized_image_service.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/models/message.dart';

/// Optimized image sender for WhatsApp-like performance
class OptimizedImageSender {
  static final OptimizedImageSender _instance =
      OptimizedImageSender._internal();
  factory OptimizedImageSender() => _instance;
  OptimizedImageSender._internal();

  /// Send multiple images efficiently with progress tracking
  static Future<ImageSendingResult> sendImagesInBatch({
    required String chatId,
    required List<ProcessedImage> images,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
    Function(int sent, int total)? onProgress,
    Function(String imageId, MessageStatus status)? onImageStatusUpdate,
  }) async {
    if (images.isEmpty) {
      return ImageSendingResult(
        success: false,
        errorMessage: 'No images to send',
        sentCount: 0,
        totalCount: 0,
      );
    }

    final List<String> sentMessageIds = [];
    final List<String> failedImages = [];
    int sentCount = 0;

    try {
      // Send images in optimized batches for maximum speed
      const batchSize =
          12; // Optimized batch size matching background upload concurrency

      // Build a stable base time so all images in this request share the same second and preserve order with small offsets
      final DateTime baseTime = DateTime.now();

      for (int i = 0; i < images.length; i += batchSize) {
        final batch = images.skip(i).take(batchSize).toList();

        // Send batch concurrently
        final futures = batch.asMap().entries.map((entry) {
          final index = i + entry.key;
          final image = entry.value;

          return _sendSingleImage(
            chatId: chatId,
            image: image,
            replyToMessageId:
                index == 0
                    ? replyToMessageId
                    : null, // Only first image has reply
            replyToText: index == 0 ? replyToText : null,
            replyToSenderName: index == 0 ? replyToSenderName : null,
            onStatusUpdate: onImageStatusUpdate,
            // Order guarantee: pass deterministic timestamp and batch metadata
            timestampOverride: baseTime.add(Duration(milliseconds: index)),
            extraMetadata: {'batchIndex': index, 'batchTotal': images.length},
          );
        });

        final batchResults = await Future.wait(futures);

        // Process batch results
        for (int j = 0; j < batchResults.length; j++) {
          final result = batchResults[j];
          final imageIndex = i + j;

          if (result.success && result.messageId != null) {
            sentMessageIds.add(result.messageId!);
            sentCount++;
          } else {
            failedImages.add(images[imageIndex].name);
          }

          onProgress?.call(sentCount, images.length);
        }

        // Small delay between batches to prevent server overload
        if (i + batchSize < images.length) {
          await Future.delayed(const Duration(milliseconds: 120));
        }
      }

      final success = sentCount > 0;
      final errorMessage =
          failedImages.isNotEmpty
              ? 'Failed to send ${failedImages.length} images: ${failedImages.join(', ')}'
              : null;

      return ImageSendingResult(
        success: success,
        sentMessageIds: sentMessageIds,
        failedImages: failedImages,
        sentCount: sentCount,
        totalCount: images.length,
        errorMessage: errorMessage,
      );
    } catch (e) {
      return ImageSendingResult(
        success: false,
        errorMessage: 'Failed to send images: $e',
        sentCount: sentCount,
        totalCount: images.length,
        failedImages: images.map((img) => img.name).toList(),
      );
    }
  }

  /// Send a single image with status tracking
  static Future<SingleImageResult> _sendSingleImage({
    required String chatId,
    required ProcessedImage image,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
    Function(String imageId, MessageStatus status)? onStatusUpdate,
    DateTime? timestampOverride,
    Map<String, dynamic>? extraMetadata,
  }) async {
    try {
      // Notify that sending has started
      onStatusUpdate?.call(image.name, MessageStatus.sending);

      // Send the image using the existing chat service
      await ChatService.sendImageMessage(
        chatId: chatId,
        imageFile: image.compressedFile,
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
        timestampOverride: timestampOverride,
        extraMetadata: extraMetadata,
      );

      // Assume success if no exception is thrown
      onStatusUpdate?.call(image.name, MessageStatus.sent);
      return SingleImageResult(
        success: true,
        messageId:
            DateTime.now().millisecondsSinceEpoch.toString(), // Temporary ID
      );
    } catch (e) {
      onStatusUpdate?.call(image.name, MessageStatus.failed);
      return SingleImageResult(
        success: false,
        errorMessage: 'Error sending image: $e',
      );
    }
  }

  /// Send images with optimized UI feedback
  static Future<ImageSendingResult> sendImagesWithFeedback({
    required BuildContext context,
    required String chatId,
    required List<ProcessedImage> images,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
    bool showProgressDialog = true,
  }) async {
    if (images.isEmpty) {
      return ImageSendingResult(
        success: false,
        errorMessage: 'No images to send',
        sentCount: 0,
        totalCount: 0,
      );
    }

    // Show progress dialog if requested
    if (showProgressDialog && context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => ImageSendingProgressDialog(totalImages: images.length),
      );
    }

    try {
      final result = await sendImagesInBatch(
        chatId: chatId,
        images: images,
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
        onProgress: (sent, total) {
          // Update progress dialog if it's showing
          if (showProgressDialog && context.mounted) {
            // Find and update the progress dialog
            // This would require a more sophisticated state management approach
          }
        },
      );

      // Close progress dialog
      if (showProgressDialog && context.mounted) {
        Navigator.of(context).pop();
      }

      return result;
    } catch (e) {
      // Close progress dialog on error
      if (showProgressDialog && context.mounted) {
        Navigator.of(context).pop();
      }

      return ImageSendingResult(
        success: false,
        errorMessage: 'Failed to send images: $e',
        sentCount: 0,
        totalCount: images.length,
      );
    }
  }
}

/// Result of sending multiple images
class ImageSendingResult {
  final bool success;
  final List<String> sentMessageIds;
  final List<String> failedImages;
  final int sentCount;
  final int totalCount;
  final String? errorMessage;

  ImageSendingResult({
    required this.success,
    this.sentMessageIds = const [],
    this.failedImages = const [],
    required this.sentCount,
    required this.totalCount,
    this.errorMessage,
  });

  bool get hasPartialSuccess => sentCount > 0 && sentCount < totalCount;
  bool get hasCompleteFailure => sentCount == 0;
  double get successRate => totalCount > 0 ? sentCount / totalCount : 0.0;
}

/// Result of sending a single image
class SingleImageResult {
  final bool success;
  final String? messageId;
  final String? errorMessage;

  SingleImageResult({required this.success, this.messageId, this.errorMessage});
}

/// Progress dialog for image sending
class ImageSendingProgressDialog extends StatefulWidget {
  final int totalImages;

  const ImageSendingProgressDialog({super.key, required this.totalImages});

  @override
  State<ImageSendingProgressDialog> createState() =>
      _ImageSendingProgressDialogState();
}

class _ImageSendingProgressDialogState extends State<ImageSendingProgressDialog>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  int _sentCount = 0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void updateProgress(int sentCount) {
    if (mounted) {
      setState(() {
        _sentCount = sentCount;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final progress =
        widget.totalImages > 0 ? _sentCount / widget.totalImages : 0.0;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedBuilder(
              animation: _scaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: const Color(0xFF25D366).withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.send,
                      color: Color(0xFF25D366),
                      size: 40,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 24),
            Text(
              'Sending Images',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '$_sentCount of ${widget.totalImages} sent',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.grey[300],
              valueColor: const AlwaysStoppedAnimation<Color>(
                Color(0xFF25D366),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
