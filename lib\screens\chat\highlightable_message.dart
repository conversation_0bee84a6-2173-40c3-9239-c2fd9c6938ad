import 'package:flutter/material.dart';

class HighlightableMessage extends StatefulWidget {
  final Widget child;
  final bool isHighlighted;
  const HighlightableMessage({Key? key, required this.child, required this.isHighlighted}) : super(key: key);

  @override
  HighlightableMessageState  createState() => HighlightableMessageState ();
}

class HighlightableMessageState  extends State<HighlightableMessage> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Color?> _colorAnimation;
  bool _wasHighlighted = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _colorAnimation = ColorTween(
      begin: const Color.fromARGB(255, 255, 255, 255).withOpacity(0.5),
      end: Colors.transparent,
    ).animate(_controller);
    if (widget.isHighlighted) {
      _controller.forward(from: 0);
      _wasHighlighted = true;
    }
  }

  @override
  void didUpdateWidget(covariant HighlightableMessage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isHighlighted && !_wasHighlighted) {
      _controller.forward(from: 0);
      _wasHighlighted = true;
    } else if (!widget.isHighlighted && _wasHighlighted) {
      _wasHighlighted = false;
    }
  }

  void triggerHighlight() {
    _controller.forward(from: 0);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _colorAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            color: _colorAnimation.value,
            borderRadius: BorderRadius.circular(16),
          ),
          child: widget.child,
        );
      },
    );
  }
}
