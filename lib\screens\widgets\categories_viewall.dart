import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/screens/widgets/categories_section.dart';
import 'package:mr_garments_mobile/services/category_service.dart';

class CategoriesViewall extends StatefulWidget {
  const CategoriesViewall({super.key});

  @override
  State<CategoriesViewall> createState() => _CategoriesViewallState();
}

class _CategoriesViewallState extends State<CategoriesViewall> {
  List<Map<String, String>> categoryData = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchCategories();
  }

  Future<void> _fetchCategories() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      final categories = await CategoryService.fetchCategories();
      categoryData = categories
          .map<Map<String, String>>((c) => {
                'image': c['imageUrl'] as String? ?? '',
                'title': c['categoryName'] as String? ?? '',
              })
          .where((c) => c['image']!.isNotEmpty && c['title']!.isNotEmpty)
          .toList();
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('All Categories'),
        foregroundColor: Colors.white,
        backgroundColor: const Color(0xFF005368),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(14),
                  child: CategoriesSection(
                    categories: categoryData,
                    maxItems: null,
                  ),
                ),
    );
  }
}