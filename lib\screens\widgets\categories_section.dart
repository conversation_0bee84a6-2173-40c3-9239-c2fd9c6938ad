import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/screens/categories/category_brands.dart';

class CategoriesSection extends StatelessWidget {
  final List<Map<String, dynamic>> categories;
  final VoidCallback? onViewAll;
  final int? maxItems;
  final bool enableNavigation;

  const CategoriesSection({
    super.key,
    required this.categories,
    this.onViewAll,
    this.maxItems = 6,
    this.enableNavigation = false,
  });

  Widget _buildCategoryGridItem(
    BuildContext context,
    Map<String, dynamic> category,
  ) {
    final imagePath = category['image'] as String?;
    final title = category['title'] ?? category['categoryName'] ?? 'Category';
    final categoryId = category['id'] as int?;

    Widget child = Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFE8F0FA), Color(0xFFF5FAFF)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(25),
            blurRadius: 6,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            // Handle both asset images and network images
            if (imagePath != null && imagePath.startsWith('assets/'))
              Image.asset(
                imagePath,
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
              )
            else if (imagePath != null && imagePath.isNotEmpty)
              Image.network(
                imagePath,
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: const Color(0xFFE8F0FA),
                    child: const Icon(
                      Icons.category,
                      color: Color(0xFF005368),
                      size: 32,
                    ),
                  );
                },
              )
            else
              Container(
                color: const Color(0xFFE8F0FA),
                child: const Icon(
                  Icons.category,
                  color: Color(0xFF005368),
                  size: 32,
                ),
              ),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.black.withAlpha(153), Colors.transparent],
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                ),
              ),
            ),
            Center(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.poppins(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );

    // Add navigation if enabled and categoryId is available
    if (enableNavigation && categoryId != null) {
      return GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (_) => CategoryBrandsScreen(
                    categoryId: categoryId,
                    categoryName: title,
                  ),
            ),
          );
        },
        child: child,
      );
    }

    return child;
  }

  @override
  Widget build(BuildContext context) {
    final displayList =
        (maxItems != null && maxItems! > 0 && categories.length > maxItems!)
            ? categories.sublist(0, maxItems)
            : categories;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'CATEGORIES',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF005368),
              ),
            ),
            if (onViewAll != null)
              GestureDetector(
                onTap: onViewAll,
                child: Text(
                  'View All',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: const Color(0xFFF2A738),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 10),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 10,
            mainAxisSpacing: 10,
            childAspectRatio: 1,
          ),
          itemCount: displayList.length,
          itemBuilder: (context, index) {
            final item = displayList[index];
            return _buildCategoryGridItem(context, item);
          },
        ),
      ],
    );
  }
}
