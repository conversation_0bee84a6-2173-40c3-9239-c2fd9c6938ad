import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mr_garments_mobile/services/manufacturer_servive.dart';

class ManufacturersState {
  final AsyncValue<List<dynamic>> manufacturers;
  final AsyncValue<Map<String, dynamic>> manufacturerDetails;

  const ManufacturersState({
    required this.manufacturers,
    required this.manufacturerDetails,
  });

  // Used to update one part of the state without affecting the other
  ManufacturersState copyWith({
    AsyncValue<List<dynamic>>? manufacturers,
    AsyncValue<Map<String, dynamic>>? manufacturerDetails,
  }) => ManufacturersState(
    manufacturers: manufacturers ?? this.manufacturers,
    manufacturerDetails: manufacturerDetails ?? this.manufacturerDetails,
  );
}

final manufacturersProvider =
    StateNotifierProvider<ManufacturersNotifier, ManufacturersState>(
      (ref) => ManufacturersNotifier(),
    );

class ManufacturersNotifier extends StateNotifier<ManufacturersState> {
  ManufacturersNotifier()
    : super(
        const ManufacturersState(
          manufacturers: AsyncLoading(),
          manufacturerDetails: AsyncData({}),
        ),
      ) {
    fetchManufacturers();
  }

  /// Fetch all manufacturers
  Future<void> fetchManufacturers() async {
    try {
      final manufacturers = await ManufacturerService.fetchAllManufacturers();
      state = state.copyWith(
        manufacturers: AsyncData(manufacturers.reversed.toList()),
      );
    } catch (e, st) {
      state = state.copyWith(manufacturers: AsyncError(e, st));
    }
  }

  /// Fetch details of a manufacturer by ID
  Future<void> fetchManufacturerDetails(int id) async {
    state = state.copyWith(manufacturerDetails: const AsyncLoading());
    try {
      final details = await ManufacturerService.fetchManufacturerDetails(id);
      state = state.copyWith(manufacturerDetails: AsyncData(details));
    } catch (e, st) {
      state = state.copyWith(manufacturerDetails: AsyncError(e, st));
    }
  }

  /// Add manufacturer
  Future<void> addManufacturer(Map<String, dynamic> data) async {
    await ManufacturerService.addManufacturer(data);
    await fetchManufacturers();
  }

  /// Update manufacturer
  Future<void> updateManufacturer(int id, Map<String, dynamic> data) async {
    await ManufacturerService.updateManufacturer(id, data);
    await fetchManufacturers();
    // await fetchUsers();
  }

  Future<Map<String, dynamic>> editManufacturerDetails(int id) async {
    return await ManufacturerService.getManufacturerDetails(id);
  }

  /// Deactivate manufacturer
  Future<void> deactivateManufacturer(int id) async {
    await ManufacturerService.deactivateManufacturer(id);
    await fetchManufacturers();
  }
}

final manufacturerDetailsProvider =
    FutureProvider.family<Map<String, dynamic>, int>((
      ref,
      manufacturerId,
    ) async {
      return ManufacturerService.fetchManufacturerDetails(manufacturerId);
    });
