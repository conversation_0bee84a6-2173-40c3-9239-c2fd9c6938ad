import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mr_garments_mobile/services/optimized_image_service.dart';
import 'package:mr_garments_mobile/providers/chat_provider.dart';


/// Corrected optimized image sender that works with existing ChatService
class CorrectedOptimizedImageSender {
  /// Send multiple images efficiently using existing message provider
  static Future<ImageSendingResult> sendImagesWithProvider({
    required WidgetRef ref,
    required String chatId,
    required List<ProcessedImage> images,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
    Function(int sent, int total)? onProgress,
  }) async {
    if (images.isEmpty) {
      return ImageSendingResult(
        success: false,
        errorMessage: 'No images to send',
        sentCount: 0,
        totalCount: 0,
      );
    }

    final messageNotifier = ref.read(messageProvider(chatId).notifier);
    final List<String> sentMessageIds = [];
    final List<String> failedImages = [];
    int sentCount = 0;

    try {
      // Send images one by one using existing message provider
      for (int i = 0; i < images.length; i++) {
        final image = images[i];
        
        try {
          // Use existing sendImageMessage method
          final success = await messageNotifier.sendImageMessage(
            image.compressedFile,
            replyToMessageId: i == 0 ? replyToMessageId : null, // Only first image has reply
          );

          if (success) {
            sentMessageIds.add(DateTime.now().millisecondsSinceEpoch.toString());
            sentCount++;
          } else {
            failedImages.add(image.name);
          }
        } catch (e) {
          failedImages.add(image.name);
          print('Failed to send image ${image.name}: $e');
        }

        onProgress?.call(sentCount, images.length);

        // Small delay between images to prevent overwhelming the server
        if (i < images.length - 1) {
          await Future.delayed(const Duration(milliseconds: 300));
        }
      }

      final success = sentCount > 0;
      final errorMessage = failedImages.isNotEmpty 
          ? 'Failed to send ${failedImages.length} images: ${failedImages.join(', ')}'
          : null;

      return ImageSendingResult(
        success: success,
        sentMessageIds: sentMessageIds,
        failedImages: failedImages,
        sentCount: sentCount,
        totalCount: images.length,
        errorMessage: errorMessage,
      );

    } catch (e) {
      return ImageSendingResult(
        success: false,
        errorMessage: 'Failed to send images: $e',
        sentCount: sentCount,
        totalCount: images.length,
        failedImages: images.map((img) => img.name).toList(),
      );
    }
  }
}

/// Result of sending multiple images
class ImageSendingResult {
  final bool success;
  final List<String> sentMessageIds;
  final List<String> failedImages;
  final int sentCount;
  final int totalCount;
  final String? errorMessage;

  ImageSendingResult({
    required this.success,
    this.sentMessageIds = const [],
    this.failedImages = const [],
    required this.sentCount,
    required this.totalCount,
    this.errorMessage,
  });

  bool get hasPartialSuccess => sentCount > 0 && sentCount < totalCount;
  bool get hasCompleteFailure => sentCount == 0;
  double get successRate => totalCount > 0 ? sentCount / totalCount : 0.0;
}