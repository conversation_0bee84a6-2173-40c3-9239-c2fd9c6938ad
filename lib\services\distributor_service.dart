import 'dart:convert';
import 'package:http/http.dart' as http;

class DistributorService {
  static const String baseUrl = 'https://mrgarment.braincavesoft.com/api';

  // Get all distributors
  static Future<List<dynamic>> fetchAllDistributors() async {
    final response = await http.get(Uri.parse('$baseUrl/distributors'));
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to load distributors');
    }
  }

  // Get distributor details by ID
  static Future<Map<String, dynamic>> fetchDistributorDetails(
    int distributorId,
  ) async {
    final response = await http.get(
      Uri.parse('$baseUrl/distributors/$distributorId'),
    );
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to fetch distributor details');
    }
  }

  // update distributor status
  static Future<Map<String, dynamic>> updateDistributorStatus(
    int id,
    String status,
  ) async {
    final url = Uri.parse('$baseUrl/distributors/$id/status');
    final response = await http.patch(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'status': status}),
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to deactivate distributor');
    }
  }  

  static Future<Map<String, dynamic>> addDistributor(
    Map<String, dynamic> data,
  ) async {
    final url = Uri.parse('$baseUrl/distributors');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(data),
    );

    if (response.statusCode == 200 || response.statusCode == 201) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to create distributor');
    }
  }

  static Future<Map<String, dynamic>> updateDistributor(
    int id,
    Map<String, dynamic> data,
  ) async {
    final url = Uri.parse('$baseUrl/distributors/$id');
    final response = await http.put(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(data),
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception(
        'Failed to update distributor: ${response.statusCode} ${response.body}',
      );
    }
  }

  static Future<Map<String, dynamic>> getDistributorDetails(int id) async {
    final url = Uri.parse('$baseUrl/distributors/$id');
    final response = await http.get(url);

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to fetch distributor details');
    }
  }
}
