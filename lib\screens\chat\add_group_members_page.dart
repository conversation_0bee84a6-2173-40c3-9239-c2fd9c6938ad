import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/models/chat_user.dart';
import 'package:mr_garments_mobile/models/group.dart';
import 'package:mr_garments_mobile/providers/chat_provider.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class AddGroupMembersPage extends ConsumerStatefulWidget {
  final String groupId;
  final String groupName;
  final Group group;

  const AddGroupMembersPage({
    super.key,
    required this.groupId,  
    required this.groupName,
    required this.group,
  });

  @override
  ConsumerState<AddGroupMembersPage> createState() =>
      _AddGroupMembersPageState();
}

class _AddGroupMembersPageState extends ConsumerState<AddGroupMembersPage> {
  final TextEditingController _searchController = TextEditingController();
  List<ChatUser> _allUsers = [];
  List<ChatUser> _filteredUsers = [];
  List<ChatUser> _selectedUsers = [];
  bool _isLoading = true;
  bool _isAdding = false;

  @override
  void initState() {
    super.initState();
    _loadUsers();
    _searchController.addListener(_filterUsers);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadUsers() async {
    try {
      setState(() => _isLoading = true);

      final users = await ChatService.getAllUsers();

      // Filter out users who are already members of the group
      final existingMemberIds =
          widget.group.members.map((m) => m.userId).toSet();
      final availableUsers =
          users.where((user) => !existingMemberIds.contains(user.id)).toList();

      setState(() {
        _allUsers = availableUsers;
        _filteredUsers = availableUsers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        AppSnackbar.showError(context, 'Error loading users: $e');
      }
    }
  }

  void _filterUsers() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredUsers =
          _allUsers.where((user) {
            return user.name.toLowerCase().contains(query) ||
                user.email.toLowerCase().contains(query);
          }).toList();
    });
  }

  void _toggleUserSelection(ChatUser user) {
    setState(() {
      if (_selectedUsers.contains(user)) {
        _selectedUsers.remove(user);
      } else {
        _selectedUsers.add(user);
      }
    });
  }

  Future<void> _addSelectedMembers() async {
    if (_selectedUsers.isEmpty) {
      AppSnackbar.showError(
        context,
        'Please select at least one member to add',
      );
      return;
    }

    try {
      setState(() => _isAdding = true);

      int successCount = 0;
      List<String> failedUsers = [];

      // Add each selected user to the group
      for (final user in _selectedUsers) {
        try {
          // First ensure the user exists in Firebase
          await ChatService.createOrUpdateUser(user);

          // Then add to group
          await ChatService.addMemberToGroup(widget.groupId, user.id);
          successCount++;
        } catch (e) {
          debugPrint('Failed to add user ${user.name}: $e');
          failedUsers.add(user.name);
        }
      }

      if (mounted) {
        if (successCount > 0) {
          AppSnackbar.showSuccess(
            context,
            '$successCount member${successCount > 1 ? 's' : ''} added successfully!',
          );

          // Refresh the group data to show new members immediately
          ref.invalidate(groupProvider(widget.groupId));
          ref.invalidate(groupChatsStreamProvider);

          Navigator.pop(context, true); // Return true to indicate success
        } else {
          AppSnackbar.showError(
            context,
            'Failed to add members: ${failedUsers.join(', ')}',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        AppSnackbar.showError(context, 'Error adding members: $e');
      }
    } finally {
      setState(() => _isAdding = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'Add Members',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF005368),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (_selectedUsers.isNotEmpty)
            TextButton(
              onPressed: _isAdding ? null : _addSelectedMembers,
              child:
                  _isAdding
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                      : Text(
                        'Add (${_selectedUsers.length})',
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
            ),
        ],
      ),
      body: Column(
        children: [
          // Group Info Header
          Container(
            width: double.infinity,
            color: const Color(0xFF005368),
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
            child: Text(
              'Adding members to "${widget.groupName}"',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.white.withValues(alpha: 0.9),
              ),
            ),
          ),

          // Search Bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search users...',
                hintStyle: GoogleFonts.poppins(color: Colors.grey[500]),
                prefixIcon: const Icon(
                  LucideIcons.search,
                  color: Color(0xFF005368),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF005368)),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
          ),

          // Selected Users Preview
          if (_selectedUsers.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Selected (${_selectedUsers.length})',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF005368),
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 60,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: _selectedUsers.length,
                      itemBuilder: (context, index) {
                        final user = _selectedUsers[index];
                        return Container(
                          margin: const EdgeInsets.only(right: 12),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CircleAvatar(
                                radius: 18,
                                backgroundColor: const Color(0xFF005368),
                                backgroundImage:
                                    user.profileImageUrl != null
                                        ? NetworkImage(user.profileImageUrl!)
                                        : null,
                                child:
                                    user.profileImageUrl == null
                                        ? Text(
                                          user.name.isNotEmpty
                                              ? user.name[0].toUpperCase()
                                              : 'U',
                                          style: GoogleFonts.poppins(
                                            color: Colors.white,
                                            fontWeight: FontWeight.w600,
                                            fontSize: 12,
                                          ),
                                        )
                                        : null,
                              ),
                              const SizedBox(height: 4),
                              SizedBox(
                                width: 36,
                                child: Text(
                                  user.name.split(' ').first,
                                  style: GoogleFonts.poppins(
                                    fontSize: 9,
                                    color: Colors.grey[600],
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),

          // Users List
          Expanded(
            child:
                _isLoading
                    ? const Center(
                      child: CircularProgressIndicator(
                        color: Color(0xFF005368),
                      ),
                    )
                    : _filteredUsers.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: _filteredUsers.length,
                      itemBuilder: (context, index) {
                        final user = _filteredUsers[index];
                        final isSelected = _selectedUsers.contains(user);
                        return _buildUserTile(user, isSelected);
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            LucideIcons.userX,
            size: 80,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No Users Available',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'All users are already members of this group',
            style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildUserTile(ChatUser user, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color:
            isSelected
                ? const Color(0xFF005368).withValues(alpha: 0.1)
                : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? const Color(0xFF005368) : Colors.grey[200]!,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: ListTile(
        onTap: () => _toggleUserSelection(user),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: CircleAvatar(
          radius: 25,
          backgroundColor: const Color(0xFF005368),
          backgroundImage:
              user.profileImageUrl != null
                  ? NetworkImage(user.profileImageUrl!)
                  : null,
          child:
              user.profileImageUrl == null
                  ? Text(
                    user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  )
                  : null,
        ),
        title: Text(
          user.name,
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
            fontSize: 16,
            color: const Color(0xFF005368),
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              user.email,
              style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[600]),
            ),
            const SizedBox(height: 2),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: _getRoleColor(user.role).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                user.role.toUpperCase(),
                style: GoogleFonts.poppins(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: _getRoleColor(user.role),
                ),
              ),
            ),
          ],
        ),
        trailing:
            isSelected
                ? const Icon(
                  LucideIcons.checkCircle,
                  color: Color(0xFF005368),
                  size: 24,
                )
                : const Icon(LucideIcons.circle, color: Colors.grey, size: 24),
      ),
    );
  }

  Color _getRoleColor(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return Colors.red;
      case 'manufacturer':
        return Colors.blue;
      case 'distributor':
        return Colors.green;
      case 'retailer':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}
