import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/screens/Auth/forgot_password_request_screen.dart';
import 'package:mr_garments_mobile/screens/Auth/onboarding_screen.dart';
import 'package:mr_garments_mobile/screens/Auth/widgets/glassmorphic_card.dart';
import 'package:mr_garments_mobile/screens/admin/admin_home_page.dart';
import 'package:mr_garments_mobile/screens/distributor/distributor_homescreen/distributor_home.dart';
import 'package:mr_garments_mobile/screens/manufacturer/Manufacturer_homescreen/manufacturer_home.dart';
import 'package:mr_garments_mobile/screens/retailer/retailer_homescreen/retailer_home.dart';
import 'package:mr_garments_mobile/services/auth_service.dart';
import 'package:mr_garments_mobile/services/credential_service.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;
  bool _rememberMe = false;
  bool _isLoadingCredentials = true;

  @override
  void initState() {
    super.initState();
    _loadSavedCredentials();
  }

  /// Load saved credentials if Remember Me was previously enabled
  Future<void> _loadSavedCredentials() async {
    try {
      final isRememberMeEnabled = await CredentialService.isRememberMeEnabled();
      final savedCredentials = await CredentialService.getSavedCredentials();

      if (mounted) {
        setState(() {
          _rememberMe = isRememberMeEnabled;
          if (savedCredentials != null) {
            _emailController.text = savedCredentials['email'] ?? '';
            _passwordController.text = savedCredentials['password'] ?? '';
          }
          _isLoadingCredentials = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingCredentials = false;
        });
      }
    }
  }

  Widget _buildTextField(
    String hint,
    TextEditingController controller, {
    bool isPassword = false,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      obscureText: isPassword && _obscurePassword,
      validator: (value) {
        if (value!.isEmpty) return 'Please enter $hint';
        if (hint == "Password" && value.length < 6) {
          return 'Password must be at least 6 characters';
        }
        if (hint == "Email" &&
            !RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return 'Enter a valid email';
        }
        return null;
      },
      style: GoogleFonts.poppins(color: Colors.black87, fontSize: 14),
      decoration: InputDecoration(
        hintText: hint,
        hintStyle: GoogleFonts.poppins(color: Colors.black, fontSize: 14),
        filled: true,
        fillColor: Colors.white,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 14,
          vertical: 16,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        floatingLabelBehavior: FloatingLabelBehavior.never,
        suffixIcon:
            isPassword
                ? IconButton(
                  icon: Icon(
                    _obscurePassword ? Icons.visibility_off : Icons.visibility,
                    color: Colors.grey[700],
                  ),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                )
                : null,
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Show loading indicator while credentials are being loaded
    if (_isLoadingCredentials) {
      return const Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: CircularProgressIndicator(color: Color(0xFF00536B)),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            GlassmorphicCard(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset(
                      'assets/images/logo.png',
                      height: 130,
                      width: 130,
                    ),
                    const SizedBox(height: 20),
                    Text(
                      "Welcome Back",
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        color: const Color(0xFF00536B),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          _buildTextField(
                            "Email",
                            _emailController,
                            keyboardType: TextInputType.emailAddress,
                          ),
                          const SizedBox(height: 16),
                          _buildTextField(
                            "Password",
                            _passwordController,
                            isPassword: true,
                          ),
                          const SizedBox(height: 10),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Transform.scale(
                                    scale: 0.75,
                                    child: Checkbox(
                                      value: _rememberMe,
                                      onChanged: (value) {
                                        setState(() {
                                          _rememberMe = value!;
                                        });
                                      },
                                      activeColor: const Color(0xFF00536B),
                                    ),
                                  ),
                                  Text(
                                    "Remember me",
                                    style: GoogleFonts.poppins(
                                      color: Colors.black,
                                      fontSize: 11.5,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                              GestureDetector(
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder:
                                          (context) =>
                                              ForgotPasswordRequestScreen(),
                                    ),
                                  );
                                },
                                child: Text(
                                  "Forgot Password?",
                                  style: GoogleFonts.poppins(
                                    color: const Color(0xFF00536B),
                                    fontWeight: FontWeight.w600,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 20),
                          ElevatedButton(
                            onPressed: () async {
                              FocusScope.of(context).unfocus();
                              if (_formKey.currentState!.validate()) {
                                showDialog(
                                  context: context,
                                  barrierDismissible: false,
                                  builder:
                                      (_) => const Center(
                                        child: CircularProgressIndicator(),
                                      ),
                                );
                                // Login logic
                                try {
                                  final result =
                                      await AuthService.loginWithSession(
                                        email: _emailController.text.trim(),
                                        password:
                                            _passwordController.text.trim(),
                                      );
                                  if (!context.mounted) return;
                                  Navigator.pop(
                                    context,
                                  ); // close loading dialog

                                  if (result['success'] == true) {
                                    // Handle Remember Me functionality
                                    if (_rememberMe) {
                                      // Save credentials for future use
                                      try {
                                        await CredentialService.saveCredentials(
                                          email: _emailController.text.trim(),
                                          password:
                                              _passwordController.text.trim(),
                                        );
                                      } catch (e) {
                                        // Silently handle credential saving errors
                                      }
                                    } else {
                                      // Clear any previously saved credentials
                                      try {
                                        await CredentialService.clearCredentials();
                                      } catch (e) {
                                        // Silently handle credential clearing errors
                                      }
                                    }

                                    final accountType =
                                        result['user']['account_type']
                                            ?.toLowerCase();

                                    Widget homeScreen;
                                    if (accountType == 'manufacturer') {
                                      homeScreen =
                                          const ManufacturerHomeScreen();
                                    } else if (accountType == 'retailer') {
                                      homeScreen = const RetailerHomeScreen();
                                    } else if (accountType == 'distributor') {
                                      homeScreen =
                                          const DistributorHomeScreen();
                                    } else if (accountType == 'admin') {
                                      homeScreen = const AdminHomePage();
                                    } else if (accountType == 'salesperson') {
                                      // SalesPerson users navigate to admin home screen
                                      homeScreen = const AdminHomePage();
                                    } else if (accountType == 'staff') {
                                      // Staff members navigate to their company's home screen
                                      final companyType =
                                          result['user']['company_type']
                                              ?.toString()
                                              .toLowerCase();
                                      switch (companyType) {
                                        case 'manufacturer':
                                          homeScreen =
                                              const ManufacturerHomeScreen();
                                          break;
                                        case 'retailer':
                                          homeScreen =
                                              const RetailerHomeScreen();
                                          break;
                                        case 'distributor':
                                          homeScreen =
                                              const DistributorHomeScreen();
                                          break;
                                        default:
                                          // Default to manufacturer if company type is unknown
                                          homeScreen =
                                              const ManufacturerHomeScreen();
                                      }
                                    } else {
                                      // Unknown account type
                                      if (!context.mounted) return;
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        const SnackBar(
                                          content: Text('Unknown account type'),
                                        ),
                                      );
                                      return;
                                    }

                                    // Navigate to home screen
                                    if (!context.mounted) return;
                                    Navigator.pushAndRemoveUntil(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => homeScreen,
                                      ),
                                      (_) => false,
                                    );
                                  } else {
                                    if (!context.mounted) return;
                                    AppSnackbar.showError(
                                      context,
                                      result['message'] ?? 'Login failed',
                                    );
                                  }
                                } catch (e) {
                                  Navigator.pop(
                                    context,
                                  ); // close loading dialog
                                  if (context.mounted) {
                                    AppSnackbar.showError(
                                      context,
                                      'Error: ${e.toString()}',
                                    );
                                  }
                                }
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF00536B),
                              minimumSize: const Size.fromHeight(50),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(14),
                              ),
                              elevation: 2,
                            ),
                            child: Text(
                              "Login",
                              style: GoogleFonts.poppins(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                          ),
                          const SizedBox(height: 30),

                          GestureDetector(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) => const OnboardingScreen(),
                                ),
                              );
                            },
                            child: Text.rich(
                              TextSpan(
                                text: "Don't have an account? ",
                                style: GoogleFonts.poppins(
                                  color: Colors.black,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                                children: [
                                  TextSpan(
                                    text: "Register Now",
                                    style: GoogleFonts.poppins(
                                      color: const Color(0xFF00536B),
                                      fontSize: 13.5,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
