import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/retailer_service.dart';

// State class
class RetailersState {
  final AsyncValue<List<dynamic>> retailers;
  final AsyncValue<Map<String, dynamic>> retailerDetails;

  const RetailersState({
    required this.retailers,
    required this.retailerDetails,
  });

  RetailersState copyWith({
    AsyncValue<List<dynamic>>? retailers,
    AsyncValue<Map<String, dynamic>>? retailerDetails,
  }) {
    return RetailersState(
      retailers: retailers ?? this.retailers,
      retailerDetails: retailerDetails ?? this.retailerDetails,
    );
  }
}

// Notifier
class RetailersNotifier extends StateNotifier<RetailersState> {
  RetailersNotifier()
    : super(
        const RetailersState(
          retailers: AsyncValue.loading(),
          retailerDetails:  AsyncValue.loading(),
        ),
      ) {
    fetchRetailers(); // Fetch on initialization
  }

  Future<void> fetchRetailers() async {
    try {
      final retailers = await RetailerService.fetchAllRetailers();
      state = state.copyWith(
        retailers: AsyncValue.data(retailers.reversed.toList()),
      );
    } catch (e, st) {
      state = state.copyWith(retailers: AsyncValue.error(e, st));
    }
  }

  Future<void> fetchRetailerDetails(int id) async {
    state = state.copyWith(retailerDetails: const AsyncValue.loading());
    try {
      final details = await RetailerService.fetchRetailerDetails(id);
      state = state.copyWith(retailerDetails: AsyncValue.data(details));
    } catch (e, st) {
      state = state.copyWith(retailerDetails: AsyncValue.error(e, st));
    }
  }

  Future<void> deactivateRetailer(int id) async {
    try {
      await RetailerService.deactivateRetailer(id);
      await fetchRetailers();
    } catch (e,st) {
        state = state.copyWith(retailerDetails: AsyncValue.error(e, st));
    }
  }

  Future<void> addRetailer(Map<String, dynamic> retailer) async {
    try {
      await RetailerService.addRetailer(retailer);
      await fetchRetailers();
    } catch (e,st) {
        state = state.copyWith(retailerDetails: AsyncValue.error(e, st));
    }
  }

  Future<void> updateRetailer(int id, Map<String, dynamic> retailer) async {
    try {
      await RetailerService.updateRetailer(id, retailer);
      await fetchRetailers();
    } catch (e,st) {
        state = state.copyWith(retailerDetails: AsyncValue.error(e, st));
    }
  }

   Future<Map<String, dynamic>> editRetailerDetails(int id) async {
    try {
      final details = await RetailerService.fetchRetailerDetails(id);
      state = state.copyWith(retailerDetails: AsyncValue.data(details));
      return details;
    } catch (e, st) {
      state = state.copyWith(retailerDetails: AsyncValue.error(e, st));
      rethrow;
    }
  }

}

// provider
final retailersProvider =
    StateNotifierProvider<RetailersNotifier, RetailersState>((ref) {
      return RetailersNotifier();
    });
