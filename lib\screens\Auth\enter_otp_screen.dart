import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/screens/Auth/login_screen.dart';
import 'package:mr_garments_mobile/screens/Auth/reset_password_screen.dart';
import 'package:mr_garments_mobile/screens/Auth/widgets/glassmorphic_card.dart';
import 'package:mr_garments_mobile/services/auth_service.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class EnterOtpScreen extends StatefulWidget {
  final String email;
  const EnterOtpScreen({super.key, required this.email});

  @override
  State<EnterOtpScreen> createState() => _EnterOtpScreenState();
}

class _EnterOtpScreenState extends State<EnterOtpScreen> {
  final TextEditingController _otpController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  void _showSnackBar(String message) => AppSnackbar.showError(context, message);

  Future<void> _verifyOtp() async {
    FocusScope.of(context).unfocus();

    if (_formKey.currentState!.validate()) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => const Center(child: CircularProgressIndicator()),
      );
      try {
        final result = await AuthService.verifyOtp(
          widget.email,
          _otpController.text.trim(),
        );
        if (!mounted) return;
        Navigator.pop(context);
        if (result['success']) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (_) => ResetPasswordScreen(
                    email: widget.email,
                    otp: _otpController.text.trim(),
                  ),
            ),
          );
        } else {
          _showSnackBar(result['message']);
        }
      } catch (e) {
        if (!mounted) return;
        Navigator.pop(context);
        _showSnackBar('Something went wrong. Try again.');
      }
    }
  }

  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Stack(
            children: [
              GlassmorphicCard(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Image.asset(
                        'assets/images/logo.png',
                        height: 140,
                        width: 140,
                      ),
                      const SizedBox(height: 20),
                      Form(
                        key: _formKey,
                        child: TextFormField(
                          controller: _otpController,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            hintText: "OTP",
                            filled: true,
                            fillColor: Colors.white,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide.none,
                            ),
                          ),
                          validator: (v) {
                            if (v == null || v.trim().isEmpty) {
                              return 'Please enter OTP';
                            }

                            if (v.trim().length != 6) {
                              return 'OTP must be 6 digits';
                            }

                            if (!RegExp(r'^\d+$').hasMatch(v.trim())) {
                              return 'OTP should contain digits only';
                            }

                            return null;
                          },
                        ),
                      ),
                      const SizedBox(height: 25),
                      ElevatedButton(
                        onPressed: _verifyOtp,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xFF00536B),
                          minimumSize: const Size.fromHeight(50),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(14),
                          ),
                        ),
                        child: Text(
                          "Submit",
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                      TextButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => LoginScreen(),
                            ),
                          );
                        },
                        child: Text(
                          "Login Now",
                          style: GoogleFonts.poppins(
                            color: const Color(0xFF00536B),
                            fontSize: 13,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
