import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mr_garments_mobile/models/message.dart';

enum ChatType { individual, group }

class Chat {
  final String id;
  final ChatType type;
  final List<String> memberIds;
  final Map<String, String> memberNames; // userId -> userName
  final Map<String, String?> memberProfileUrls; // userId -> profileUrl
  final String? groupName;
  final String? groupImageUrl;
  final String? groupDescription;
  final String? createdBy;
  final Message? lastMessage;
  final DateTime? lastMessageTime;
  final Map<String, int> unreadCounts; // userId -> unreadCount
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;

  Chat({
    required this.id,
    this.type = ChatType.individual,
    required this.memberIds,
    this.memberNames = const {},
    this.memberProfileUrls = const {},
    this.groupName,
    this.groupImageUrl,
    this.groupDescription,
    this.createdBy,
    this.lastMessage,
    this.lastMessageTime,
    this.unreadCounts = const {},
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
  });

  // Convert Chat to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.name,
      'memberIds': memberIds,
      'memberNames': memberNames,
      'memberProfileUrls': memberProfileUrls,
      'groupName': groupName,
      'groupImageUrl': groupImageUrl,
      'groupDescription': groupDescription,
      'createdBy': createdBy,
      'lastMessage': lastMessage?.toMap(),
      'lastMessageTime': lastMessageTime?.millisecondsSinceEpoch,
      'unreadCounts': unreadCounts,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'isActive': isActive,
    };
  }

  // Create Chat from Firestore document
  factory Chat.fromMap(Map<String, dynamic> map) {
    return Chat(
      id: map['id'] ?? '',
      type: ChatType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => ChatType.individual,
      ),
      memberIds: List<String>.from(map['memberIds'] ?? []),
      memberNames: Map<String, String>.from(map['memberNames'] ?? {}),
      memberProfileUrls: Map<String, String?>.from(
        map['memberProfileUrls'] ?? {},
      ),
      groupName: map['groupName'],
      groupImageUrl: map['groupImageUrl'],
      groupDescription: map['groupDescription'],
      createdBy: map['createdBy'],
      lastMessage:
          map['lastMessage'] != null
              ? Message.fromMap(map['lastMessage'])
              : null,
      lastMessageTime:
          map['lastMessageTime'] != null
              ? _parseDateTime(map['lastMessageTime'])
              : null,
      unreadCounts: Map<String, int>.from(map['unreadCounts'] ?? {}),
      createdAt: _parseDateTime(map['createdAt']),
      updatedAt: _parseDateTime(map['updatedAt']),
      isActive: map['isActive'] ?? true,
    );
  }

  // Helper method to parse DateTime from various formats
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) {
      return DateTime.now();
    }

    if (value is int) {
      return DateTime.fromMillisecondsSinceEpoch(value);
    }

    if (value is Timestamp) {
      return value.toDate();
    }

    if (value is DateTime) {
      return value;
    }

    // Fallback to current time if we can't parse
    return DateTime.now();
  }

  // Create Chat from Firestore DocumentSnapshot
  factory Chat.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Chat.fromMap(data);
  }

  // Copy with method for updating chat data
  Chat copyWith({
    String? id,
    ChatType? type,
    List<String>? memberIds,
    Map<String, String>? memberNames,
    Map<String, String?>? memberProfileUrls,
    String? groupName,
    String? groupImageUrl,
    String? groupDescription,
    String? createdBy,
    Message? lastMessage,
    DateTime? lastMessageTime,
    Map<String, int>? unreadCounts,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return Chat(
      id: id ?? this.id,
      type: type ?? this.type,
      memberIds: memberIds ?? this.memberIds,
      memberNames: memberNames ?? this.memberNames,
      memberProfileUrls: memberProfileUrls ?? this.memberProfileUrls,
      groupName: groupName ?? this.groupName,
      groupImageUrl: groupImageUrl ?? this.groupImageUrl,
      groupDescription: groupDescription ?? this.groupDescription,
      createdBy: createdBy ?? this.createdBy,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      unreadCounts: unreadCounts ?? this.unreadCounts,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  // Get other member's name (for individual chats)
  String getOtherMemberName(String currentUserId) {
    if (type == ChatType.group) return groupName ?? 'Group Chat';

    final otherMemberId = memberIds.firstWhere(
      (id) => id != currentUserId,
      orElse: () => '',
    );
    return memberNames[otherMemberId] ?? 'Unknown User';
  }

  // Get other member's profile URL (for individual chats)
  String? getOtherMemberProfileUrl(String currentUserId) {
    if (type == ChatType.group) return groupImageUrl;

    final otherMemberId = memberIds.firstWhere(
      (id) => id != currentUserId,
      orElse: () => '',
    );
    return memberProfileUrls[otherMemberId];
  }

  // Get unread count for specific user
  int getUnreadCount(String userId) {
    return unreadCounts[userId] ?? 0;
  }

  // Check if chat is a group
  bool get isGroup => type == ChatType.group;

  // Get chat display name
  String getDisplayName(String currentUserId) {
    return isGroup
        ? (groupName ?? 'Group Chat')
        : getOtherMemberName(currentUserId);
  }

  // Get chat display image
  String? getDisplayImage(String currentUserId) {
    return isGroup ? groupImageUrl : getOtherMemberProfileUrl(currentUserId);
  }

  @override
  String toString() {
    return 'Chat(id: $id, type: $type, memberIds: $memberIds, groupName: $groupName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Chat && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
