import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/models/message.dart';

class CatalogMessageWidget extends StatelessWidget {
  final Message message;
  final bool isSentByMe;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const CatalogMessageWidget({
    super.key,
    required this.message,
    required this.isSentByMe,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    final catalogs = message.metadata?['catalogs'] as List<dynamic>? ?? [];
    final catalog = catalogs.isNotEmpty ? catalogs.first : null;

    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: Container(
        margin: EdgeInsets.only(
          left: isSentByMe ? 10 : 8,
          right: isSentByMe ? 8 : 10,
          bottom: 8,
        ),
        child: Row(
          mainAxisAlignment:
              isSentByMe ? MainAxisAlignment.end : MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (!isSentByMe) ...[
              // CircleAvatar(
              //   radius: 10,
              //   backgroundColor: const Color(0xFF005368),
              //   child: Text(
              //     message.senderName.isNotEmpty
              //         ? message.senderName[0].toUpperCase()
              //         : '?',
              //     style: GoogleFonts.poppins(fontSize: 10, color: Colors.white),
              //   ),
              // ),
              const SizedBox(width: 6),
            ],
            Flexible(
              child: Container(
                constraints: const BoxConstraints(maxWidth: 280),
                decoration: BoxDecoration(
                  color: isSentByMe ? const Color(0xFF005368) : const Color.fromARGB(255, 242, 248, 255),
                  borderRadius: BorderRadius.only(
                    topLeft: const Radius.circular(20),
                    topRight: const Radius.circular(20),
                    bottomLeft: Radius.circular(isSentByMe ? 20 : 4),
                    bottomRight: Radius.circular(isSentByMe ? 4 : 20),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with improved design
                    // Container(
                    //   padding: const EdgeInsets.all(12),
                    //   decoration: BoxDecoration(
                    //     color:
                    //         isSentByMe
                    //             ? Colors.white.withOpacity(0.1)
                    //             : const Color(0xFF005368).withOpacity(0.05),
                    //     borderRadius: const BorderRadius.only(
                    //       topLeft: Radius.circular(20),
                    //       topRight: Radius.circular(20),
                    //     ),
                    //   ),
                    //   child: Row(
                    //     children: [
                    //       Container(
                    //         padding: const EdgeInsets.all(8),
                    //         decoration: BoxDecoration(
                    //           gradient: LinearGradient(
                    //             begin: Alignment.topLeft,
                    //             end: Alignment.bottomRight,
                    //             colors:
                    //                 isSentByMe
                    //                     ? [
                    //                       Colors.white.withOpacity(0.3),
                    //                       Colors.white.withOpacity(0.1),
                    //                     ]
                    //                     : [
                    //                       const Color(
                    //                         0xFF005368,
                    //                       ).withOpacity(0.2),
                    //                       const Color(
                    //                         0xFF007B8A,
                    //                       ).withOpacity(0.1),
                    //                     ],
                    //           ),
                    //           borderRadius: BorderRadius.circular(12),
                    //         ),
                    //       //   child: Icon(
                    //       //     Icons.photo_library_rounded,
                    //       //     color:
                    //       //         isSentByMe
                    //       //             ? Colors.white
                    //       //             : const Color(0xFF005368),
                    //       //     size: 20,
                    //       //   ),
                    //       // ),
                    //       // const SizedBox(width: 12),
                    //       // Expanded(
                    //       //   child: Column(
                    //       //     crossAxisAlignment: CrossAxisAlignment.start,
                    //       //     children: [
                    //       //       Text(
                    //       //         'Catalog',
                    //       //         style: GoogleFonts.poppins(
                    //       //           fontSize: 16,
                    //       //           fontWeight: FontWeight.w700,
                    //       //           color:
                    //       //               isSentByMe
                    //       //                   ? Colors.white
                    //       //                   : Colors.grey[800],
                    //       //           letterSpacing: 0.3,
                    //       //         ),
                    //       //       ),
                    //       //       const SizedBox(height: 2),
                    //       //       Text(
                    //       //         catalog?['catalogNumber'] ??
                    //       //             'Unknown Catalog',
                    //       //         style: GoogleFonts.poppins(
                    //       //           fontSize: 12,
                    //       //           color:
                    //       //               isSentByMe
                    //       //                   ? Colors.white.withOpacity(0.8)
                    //       //                   : Colors.grey[600],
                    //       //           fontWeight: FontWeight.w500,
                    //       //         ),
                    //       //       ),
                    //       //     ],
                    //       //   ),
                    //       ),
                    //     ],
                    //   ),
                    // ),

                    // Catalog details
                    if (catalog != null) ...[
                      Container(
                        padding: const EdgeInsets.all(12),
                        child: Row(
                          children: [
                            Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors:
                                      isSentByMe
                                          ? [
                                            Colors.white.withOpacity(0.3),
                                            Colors.white.withOpacity(0.1),
                                          ]
                                          : [
                                            const Color(0xFF005368),
                                            const Color(0xFF007B8A),
                                          ],
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.photo_library_rounded,
                                color: isSentByMe ? Colors.white : Colors.white,
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    catalog['catalogNumber'] ??
                                        'Unknown Catalog',
                                    style: GoogleFonts.poppins(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                      color:
                                          isSentByMe
                                              ? Colors.white
                                              : Colors.grey[800],
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: (isSentByMe
                                                  ? Colors.white
                                                  : const Color(0xFF005368))
                                              .withOpacity(0.2),
                                          borderRadius: BorderRadius.circular(
                                            6,
                                          ),
                                        ),
                                        child: Text(
                                          catalog['brandName'] ??
                                              'Unknown Brand',
                                          style: GoogleFonts.poppins(
                                            fontSize: 10,
                                            fontWeight: FontWeight.w600,
                                            color:
                                                isSentByMe
                                                    ? Colors.white
                                                    : const Color(0xFF005368),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: (isSentByMe
                                                  ? Colors.white
                                                  : const Color(0xFFF2A738))
                                              .withOpacity(0.2),
                                          borderRadius: BorderRadius.circular(
                                            6,
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.photo_camera_rounded,
                                              size: 12,
                                              color:
                                                  isSentByMe
                                                      ? Colors.white
                                                      : const Color(0xFFF2A738),
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              '${catalog['photosCount'] ?? 0}',
                                              style: GoogleFonts.poppins(
                                                fontSize: 10,
                                                fontWeight: FontWeight.w600,
                                                color:
                                                    isSentByMe
                                                        ? Colors.white
                                                        : const Color(
                                                          0xFFF2A738,
                                                        ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],

                    // Action button with improved design
                    Container(
                      margin: const EdgeInsets.all(12),
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: onTap,
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              isSentByMe
                                  ? Colors.white.withOpacity(0.2)
                                  : const Color(0xFF005368),
                          foregroundColor:
                              isSentByMe ? Colors.white : Colors.white,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(
                            vertical: 10,
                            horizontal: 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        icon: const Icon(Icons.visibility_rounded, size: 16),
                        label: Text(
                          'View Catalog',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // if (isSentByMe) ...[
            //   const SizedBox(width: 8),
            //   Column(
            //     crossAxisAlignment: CrossAxisAlignment.end,
            //     children: [
            //       Text(
            //         _formatTime(message.timestamp),
            //         style: GoogleFonts.poppins(
            //           fontSize: 10,
            //           color: Colors.grey[500],
            //           fontWeight: FontWeight.w500,
            //         ),
            //       ),
            //       const SizedBox(height: 4),
            //       _buildMessageStatus(message.status),
            //     ],
            //   ),
            // ],
          ],
        ),
      ),
    );
  }

//   Widget _buildMessageStatus(MessageStatus status) {
//     IconData icon;
//     Color color;

//     switch (status) {
//       case MessageStatus.sending:
//         icon = Icons.schedule_rounded;
//         color = Colors.grey[400]!;
//         break;
//       case MessageStatus.sent:
//         icon = Icons.check_rounded;
//         color = Colors.grey[400]!;
//         break;
//       case MessageStatus.delivered:
//         icon = Icons.done_all_rounded;
//         color = Colors.grey[400]!;
//         break;
//       case MessageStatus.read:
//         icon = Icons.done_all_rounded;
//         color = const Color(0xFF005368);
//         break;
//       case MessageStatus.failed:
//         icon = Icons.error_rounded;
//         color = Colors.red;
//         break;
//     }

//     return Icon(icon, size: 16, color: color);
//   }

//   String _formatTime(DateTime time) {
//     final now = DateTime.now();
//     final today = DateTime(now.year, now.month, now.day);
//     final messageDate = DateTime(time.year, time.month, time.day);

//     if (messageDate == today) {
//       return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
//     } else if (messageDate == today.subtract(const Duration(days: 1))) {
//       return 'Yesterday';
//     } else {
//       return '${time.day}/${time.month}/${time.year}';
//     }
//   }
}
