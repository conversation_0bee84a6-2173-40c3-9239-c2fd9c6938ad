{"buildFiles": ["C:\\flutter_windows_3.24.4-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\BrainCave-1\\mr-garments-mobile\\android\\app\\.cxx\\Debug\\3a6a37n4\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\BrainCave-1\\mr-garments-mobile\\android\\app\\.cxx\\Debug\\3a6a37n4\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}