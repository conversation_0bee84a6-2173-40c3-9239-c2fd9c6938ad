import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/screens/Auth/login_screen.dart';
import 'package:mr_garments_mobile/screens/Auth/registration_success_screen.dart';
import 'package:mr_garments_mobile/screens/Auth/widgets/glassmorphic_card.dart';
import 'package:mr_garments_mobile/services/auth_service.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class RegisterScreen extends StatefulWidget {
  final String accountType;
  const RegisterScreen({super.key, required this.accountType});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _obscurePassword = true;
  bool _agreeTerms = false;

  final _nameController = TextEditingController();
  final _mobileController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  Widget _buildTextField(
    String hint,
    TextEditingController controller, {
    bool isPassword = false,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      obscureText: isPassword && _obscurePassword,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter $hint';
        }
        if (hint == "Email") {
          final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
          if (!emailRegex.hasMatch(value.trim())) {
            return 'Please enter a valid email address';
          }
        }
        if (hint == "Password" && value.length < 6) {
          return 'Password must be at least 6 characters';
        }
        if (hint == "Mobile" && (value.length < 10 || value.length > 10)) {
          return 'Enter a valid mobile number';
        }
        return null;
      },
      style: GoogleFonts.poppins(color: Colors.black87, fontSize: 14),
      decoration: InputDecoration(
        hintText: hint,
        hintStyle: GoogleFonts.poppins(color: Colors.black, fontSize: 14),
        filled: true,
        fillColor: Colors.white,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 14,
          vertical: 16,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        floatingLabelBehavior: FloatingLabelBehavior.never,
        suffixIcon:
            isPassword
                ? IconButton(
                  icon: Icon(
                    _obscurePassword ? Icons.visibility_off : Icons.visibility,
                    color: Colors.grey[700],
                  ),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                )
                : null,
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _mobileController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _showSnackBar(String message) {
    AppSnackbar.showError(context, message);
  }

  Future<void> _handleRegister() async {
    FocusScope.of(context).unfocus(); // dismiss keyboard
    if (_formKey.currentState!.validate()) {
      if (!_agreeTerms) {
        _showSnackBar('Please agree to the Terms & Conditions.');
        return;
      }

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => const Center(child: CircularProgressIndicator()),
      );

      try {
        final result = await AuthService.registerWithSession(
          name: _nameController.text.trim(),
          email: _emailController.text.trim(),
          mobile: _mobileController.text.trim(),
          password: _passwordController.text.trim(),
          accountType: widget.accountType,
          agreeTerms: _agreeTerms,
        );
        if (!mounted) return;
        Navigator.pop(context); // remove loading dialog

        if (result['success']) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (_) => const RegistrationSuccessScreen(),
            ),
          );
        } else {
          _showSnackBar(result['message'] ?? 'Registration failed');
        }
      } catch (e) {
        Navigator.pop(context); // remove loading dialog
        _showSnackBar('Something went wrong. Please try again later.');
      }
    } else {
      _showSnackBar('Please correct the highlighted fields.');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            GlassmorphicCard(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Image.asset(
                      'assets/images/logo.png',
                      height: 130,
                      width: 130,
                    ),

                    // Form Fields
                    Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          _buildTextField("Name", _nameController),
                          const SizedBox(height: 16),
                          _buildTextField(
                            "Mobile",
                            _mobileController,
                            keyboardType: TextInputType.phone,
                          ),
                          const SizedBox(height: 16),
                          _buildTextField(
                            "Email",
                            _emailController,
                            keyboardType: TextInputType.emailAddress,
                          ),
                          const SizedBox(height: 16),
                          _buildTextField(
                            "Password",
                            _passwordController,
                            isPassword: true,
                          ),
                          const SizedBox(height: 16),

                          // Checkbox
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Transform.scale(
                                scale: 0.75,
                                child: Checkbox(
                                  value: _agreeTerms,
                                  onChanged:
                                      (value) =>
                                          setState(() => _agreeTerms = value!),
                                  activeColor: Color(0xFF00536B),
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  "I agree to the Terms & Conditions",
                                  style: GoogleFonts.poppins(
                                    color: Colors.black87,
                                    fontSize: 11.5,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 20),

                          // Register Button
                          ElevatedButton(
                            onPressed: _handleRegister,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Color(0xFF00536B),
                              minimumSize: const Size.fromHeight(50),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(14),
                              ),
                              elevation: 2,
                            ),
                            child: Text(
                              "Register",
                              style: GoogleFonts.poppins(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                          ),
                          const SizedBox(height: 14),

                          // Already have account
                          GestureDetector(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => LoginScreen(),
                                ),
                              );
                            },
                            child: Text.rich(
                              TextSpan(
                                text: "Already have an account? ",
                                style: GoogleFonts.poppins(
                                  color: Colors.black87,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 11.5,
                                ),
                                children: [
                                  TextSpan(
                                    text: "Login Now",
                                    style: GoogleFonts.poppins(
                                      color: Color(0xFF00536B),
                                      fontSize: 13.5,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 10),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
