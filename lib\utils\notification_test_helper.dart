import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:mr_garments_mobile/services/notification_service.dart';
import 'package:mr_garments_mobile/services/session_service.dart';

/// Helper class for testing notification functionality
class NotificationTestHelper {
  /// Test local notification display
  static Future<void> testLocalNotification() async {
    try {
      // Create a test remote message
      final testMessage = RemoteMessage(
        messageId: 'test_${DateTime.now().millisecondsSinceEpoch}',
        data: {
          'chatId': 'test_chat_123',
          'otherUserName': 'Test User',
          'isGroup': 'false',
        },
        notification: const RemoteNotification(
          title: 'Test Notification',
          body: 'This is a test message for notification functionality',
        ),
      );

      // Show the notification
      await NotificationService.showLocalNotification(testMessage);

      debugPrint('✅ Test notification sent successfully');
    } catch (e) {
      debugPrint('❌ Test notification failed: $e');
    }
  }

  /// Test if Cloud Function is accessible
  static Future<void> testCloudFunctionAccess() async {
    try {
      debugPrint('🌐 Testing Cloud Function accessibility...');

      const functionUrl =
          'https://us-central1-mrgarments-f3b34.cloudfunctions.net/sendChatNotification';

      // Send a simple GET request to check if function exists
      final response = await http.get(Uri.parse(functionUrl));

      debugPrint('📡 Function URL: $functionUrl');
      debugPrint('📥 Response status: ${response.statusCode}');
      debugPrint('📥 Response headers: ${response.headers}');

      if (response.statusCode == 204 || response.statusCode == 200) {
        debugPrint('✅ Cloud Function is accessible');
      } else {
        debugPrint('❌ Cloud Function may not be deployed or accessible');
      }
    } catch (e) {
      debugPrint('❌ Error testing Cloud Function access: $e');
    }
  }

  /// Test backend notification endpoint
  static Future<void> testBackendNotification() async {
    try {
      final currentUserId = await SessionService.getUserId();
      if (currentUserId == null) {
        debugPrint('❌ User not logged in');
        return;
      }

      // Test the Cloud Function notification endpoint
      const backendUrl =
          'https://us-central1-mrgarments-f3b34.cloudfunctions.net/sendChatNotification';

      final payload = {
        'memberIds': [currentUserId.toString()], // Send to self for testing
        'chatId': 'test_chat_${DateTime.now().millisecondsSinceEpoch}',
        'senderName': 'Test Sender',
        'messageText': 'This is a test notification from backend!',
        'messageType': 'text',
        'senderId': 'test_sender_123',
      };

      final response = await http.post(
        Uri.parse(backendUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode(payload),
      );

      if (response.statusCode == 200) {
        debugPrint('✅ Backend notification test successful');
        debugPrint('Response: ${response.body}');
      } else {
        debugPrint(
          '❌ Backend notification test failed: ${response.statusCode}',
        );
        debugPrint('Response: ${response.body}');
      }
    } catch (e) {
      debugPrint('❌ Backend notification test error: $e');
    }
  }

  /// Get current FCM token for debugging
  static Future<String?> getCurrentFCMToken() async {
    try {
      final token = await FirebaseMessaging.instance.getToken();
      debugPrint('📱 Current FCM Token: $token');
      return token;
    } catch (e) {
      debugPrint('❌ Failed to get FCM token: $e');
      return null;
    }
  }

  /// Test notification permissions
  static Future<bool> testNotificationPermissions() async {
    try {
      final messaging = FirebaseMessaging.instance;
      final settings = await messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      final isAuthorized =
          settings.authorizationStatus == AuthorizationStatus.authorized;

      debugPrint(
        isAuthorized
            ? '✅ Notification permissions granted'
            : '❌ Notification permissions denied',
      );

      return isAuthorized;
    } catch (e) {
      debugPrint('❌ Permission test failed: $e');
      return false;
    }
  }

  /// Test notification service initialization
  static Future<bool> testNotificationServiceInit() async {
    try {
      await NotificationService.initialize();
      debugPrint('✅ Notification service initialized successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Notification service initialization failed: $e');
      return false;
    }
  }

  /// Ensure all users in the system have FCM tokens
  static Future<void> ensureAllUsersHaveFCMTokens() async {
    try {
      debugPrint('🔄 Ensuring all users have FCM tokens...');

      // Get all users from Firestore
      final usersSnapshot =
          await FirebaseFirestore.instance
              .collection('users')
              .limit(50) // Limit to prevent too many operations
              .get();

      int usersWithTokens = 0;
      int usersWithoutTokens = 0;

      for (final doc in usersSnapshot.docs) {
        final data = doc.data();
        final userId = doc.id;
        final fcmToken = data['fcmToken'];

        if (fcmToken == null || fcmToken.toString().isEmpty) {
          usersWithoutTokens++;
          debugPrint('⚠️ User $userId (${data['name']}) has no FCM token');
        } else {
          usersWithTokens++;
          debugPrint('✅ User $userId (${data['name']}) has FCM token');
        }
      }

      debugPrint('📊 Summary:');
      debugPrint('   Users with FCM tokens: $usersWithTokens');
      debugPrint('   Users without FCM tokens: $usersWithoutTokens');
      debugPrint('   Total users checked: ${usersSnapshot.docs.length}');

      if (usersWithoutTokens > 0) {
        debugPrint(
          '⚠️ Some users don\'t have FCM tokens. They need to log in again or force update.',
        );
      }
    } catch (e) {
      debugPrint('❌ Error checking user FCM tokens: $e');
    }
  }

  /// Force update FCM token in Firestore
  static Future<void> forceUpdateFCMToken() async {
    try {
      debugPrint('🔄 Force updating FCM token...');

      final currentUserId = await SessionService.getUserId();
      if (currentUserId == null) {
        debugPrint('❌ User not logged in');
        return;
      }

      final token = await FirebaseMessaging.instance.getToken();
      if (token == null) {
        debugPrint('❌ No FCM token available');
        return;
      }

      debugPrint('👤 User ID: $currentUserId');
      debugPrint('🔑 FCM Token: ${token.substring(0, 20)}...');

      // Force update the FCM token
      await FirebaseFirestore.instance
          .collection('users')
          .doc(currentUserId.toString())
          .update({
            'fcmToken': token,
            'updatedAt': DateTime.now().millisecondsSinceEpoch,
          });

      debugPrint('✅ FCM token force updated successfully');

      // Verify the update
      final doc =
          await FirebaseFirestore.instance
              .collection('users')
              .doc(currentUserId.toString())
              .get();

      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        debugPrint(
          '✅ Verified FCM token: ${data['fcmToken']?.substring(0, 20)}...',
        );
      }
    } catch (e) {
      debugPrint('❌ Error force updating FCM token: $e');
    }
  }

  /// Debug Firestore FCM tokens
  static Future<void> debugFirestoreTokens() async {
    try {
      debugPrint('🔍 Debugging Firestore FCM tokens...');

      final currentUserId = await SessionService.getUserId();
      if (currentUserId == null) {
        debugPrint('❌ User not logged in');
        return;
      }

      debugPrint('👤 Current User ID: $currentUserId');

      // Check if user document exists in chat_users collection
      final userDoc =
          await FirebaseFirestore.instance
              .collection('chat_users')
              .doc(currentUserId.toString())
              .get();

      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        debugPrint('✅ User document found in chat_users');
        debugPrint(
          '📱 FCM Token: ${userData['fcmToken']?.substring(0, 20)}...',
        );
        debugPrint('🕒 Updated At: ${userData['updatedAt']}');
        debugPrint('📄 Full data: $userData');
      } else {
        debugPrint('❌ User document NOT found in chat_users collection');

        // Check if it exists in users collection instead
        final usersDoc =
            await FirebaseFirestore.instance
                .collection('users')
                .doc(currentUserId.toString())
                .get();

        if (usersDoc.exists) {
          debugPrint(
            '⚠️ Found user in "users" collection instead of "chat_users"',
          );
          final userData = usersDoc.data() as Map<String, dynamic>;
          debugPrint('📄 Users collection data: $userData');
        }
      }

      // List all documents in chat_users collection
      final allUsers =
          await FirebaseFirestore.instance
              .collection('chat_users')
              .limit(10)
              .get();

      debugPrint('\n📋 All users in chat_users collection:');
      for (final doc in allUsers.docs) {
        final data = doc.data();
        debugPrint(
          '   ID: ${doc.id}, FCM: ${data['fcmToken']?.substring(0, 20)}...',
        );
      }
    } catch (e) {
      debugPrint('❌ Error debugging Firestore: $e');
    }
  }

  /// Run comprehensive notification test
  static Future<void> runComprehensiveTest() async {
    debugPrint('🧪 Starting comprehensive notification test...\n');

    // Test 0: Force update FCM token
    debugPrint('0️⃣ Force updating FCM token...');
    await forceUpdateFCMToken();

    // Test 0.5: Debug Firestore
    debugPrint('\n🔍 Debugging Firestore FCM tokens...');
    await debugFirestoreTokens();

    // Test 0.5: Test Cloud Function access
    debugPrint('\n🌐 Testing Cloud Function access...');
    await testCloudFunctionAccess();

    // Test 1: Permissions
    debugPrint('\n1️⃣ Testing notification permissions...');
    final hasPermissions = await testNotificationPermissions();

    // Test 2: Service initialization
    debugPrint('\n2️⃣ Testing notification service initialization...');
    final serviceInitialized = await testNotificationServiceInit();

    // Test 3: FCM token
    debugPrint('\n3️⃣ Testing FCM token retrieval...');
    final token = await getCurrentFCMToken();

    // Test 4: Local notification
    if (hasPermissions && serviceInitialized) {
      debugPrint('\n4️⃣ Testing local notification display...');
      await testLocalNotification();
    } else {
      debugPrint(
        '\n4️⃣ ⏭️ Skipping local notification test (prerequisites failed)',
      );
    }

    // Summary
    debugPrint('\n📊 Test Summary:');
    debugPrint('   Permissions: ${hasPermissions ? '✅' : '❌'}');
    debugPrint('   Service Init: ${serviceInitialized ? '✅' : '❌'}');
    debugPrint('   FCM Token: ${token != null ? '✅' : '❌'}');
    debugPrint(
      '   Overall: ${hasPermissions && serviceInitialized && token != null ? '✅ PASS' : '❌ FAIL'}',
    );
  }

  /// Show instructions for users to fix their FCM tokens
  static void showUserInstructions(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('📱 Notification Setup Required'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'To receive notifications, users need to:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 16),
                Text('1. 🔄 Log out and log back in'),
                SizedBox(height: 8),
                Text('2. 🔧 Or use "Force Update FCM Token" button'),
                SizedBox(height: 8),
                Text('3. 📱 Or restart the app'),
                SizedBox(height: 16),
                Text(
                  'This is a one-time setup. New users will automatically have notifications enabled.',
                  style: TextStyle(fontStyle: FontStyle.italic),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Got it!'),
              ),
            ],
          ),
    );
  }

  /// Create a test notification widget for manual testing
  static Widget createTestWidget() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Test'),
        backgroundColor: const Color(0xFF005368),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Notification Test Helper',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            ElevatedButton(
              onPressed: () => runComprehensiveTest(),
              child: const Text('Run Comprehensive Test'),
            ),
            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: () => forceUpdateFCMToken(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('Force Update FCM Token'),
            ),
            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: () => ensureAllUsersHaveFCMTokens(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
              ),
              child: const Text('Check All Users FCM Tokens'),
            ),
            const SizedBox(height: 16),

            Builder(
              builder:
                  (context) => ElevatedButton(
                    onPressed: () => showUserInstructions(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Show User Instructions'),
                  ),
            ),
            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: () => testLocalNotification(),
              child: const Text('Test Local Notification'),
            ),
            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: () => testBackendNotification(),
              child: const Text('Test Backend Notification'),
            ),
            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: () => getCurrentFCMToken(),
              child: const Text('Get FCM Token'),
            ),
            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: () => testNotificationPermissions(),
              child: const Text('Test Permissions'),
            ),
            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: () => debugFirestoreTokens(),
              child: const Text('Debug Firestore Tokens'),
            ),
            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: () => testCloudFunctionAccess(),
              child: const Text('Test Cloud Function Access'),
            ),
            const SizedBox(height: 32),

            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Instructions:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text('1. Run comprehensive test first'),
                    Text('2. Check debug console for results'),
                    Text('3. Test individual components if needed'),
                    Text('4. Ensure FCM server key is configured'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
