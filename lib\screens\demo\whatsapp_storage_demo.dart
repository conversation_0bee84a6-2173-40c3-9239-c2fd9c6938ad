import 'package:flutter/material.dart';
import '../../services/whatsapp_local_storage_service.dart';

/// Demo screen to showcase WhatsApp-like local storage functionality
class WhatsAppStorageDemoScreen extends StatefulWidget {
  const WhatsAppStorageDemoScreen({Key? key}) : super(key: key);

  @override
  State<WhatsAppStorageDemoScreen> createState() =>
      _WhatsAppStorageDemoScreenState();
}

class _WhatsAppStorageDemoScreenState extends State<WhatsAppStorageDemoScreen> {
  WhatsAppStorageInfo? _storageInfo;
  bool _isLoading = false;
  String? _senderPath;
  String? _receiverPath;

  @override
  void initState() {
    super.initState();
    _initializeStorage();
  }

  Future<void> _initializeStorage() async {
    setState(() => _isLoading = true);

    try {
      await WhatsAppLocalStorageService.initialize();
      await _loadStorageInfo();

      setState(() {
        _senderPath = WhatsAppLocalStorageService.senderDirectoryPath;
        _receiverPath = WhatsAppLocalStorageService.receiverDirectoryPath;
      });
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error initializing storage: $e')));
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadStorageInfo() async {
    try {
      final info = await WhatsAppLocalStorageService.getStorageInfo();
      setState(() => _storageInfo = info);
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error loading storage info: $e')));
    }
  }

  Future<void> _clearCache() async {
    setState(() => _isLoading = true);

    try {
      final success = await WhatsAppLocalStorageService.clearAllCache();
      if (success) {
        await _loadStorageInfo();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('✅ Cache cleared successfully!')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('❌ Failed to clear cache')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error clearing cache: $e')));
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _cleanupOldFiles() async {
    setState(() => _isLoading = true);

    try {
      await WhatsAppLocalStorageService.cleanupOldFiles(daysOld: 7);
      await _loadStorageInfo();
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('🧹 Old files cleaned up!')));
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error cleaning up: $e')));
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('WhatsApp Storage Demo'),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeaderCard(),
                    const SizedBox(height: 16),
                    _buildStoragePathsCard(),
                    const SizedBox(height: 16),
                    _buildStorageInfoCard(),
                    const SizedBox(height: 16),
                    _buildActionsCard(),
                    const SizedBox(height: 16),
                    _buildFlowDemoCard(),
                  ],
                ),
              ),
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.storage, color: Colors.green[700], size: 28),
                const SizedBox(width: 12),
                const Text(
                  'WhatsApp-like Storage',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Images are stored locally in Android/media/com.mrgarments/media/Images/ '
              'with Sent and Private folders (like WhatsApp), uploaded to Firebase temporarily, '
              'then downloaded by receivers and automatically cleaned up from Firebase.',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStoragePathsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '📁 Storage Paths',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildPathItem('📤 Sender', _senderPath),
            const SizedBox(height: 8),
            _buildPathItem('📥 Receiver', _receiverPath),
          ],
        ),
      ),
    );
  }

  Widget _buildPathItem(String label, String? path) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w600)),
          const SizedBox(height: 4),
          Text(
            path ?? 'Not initialized',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontFamily: 'monospace',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStorageInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '📊 Storage Information',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _loadStorageInfo,
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_storageInfo != null) ...[
              _buildStorageItem(
                '📤 Sender Storage',
                _storageInfo!.formattedSenderSize,
                '${_storageInfo!.senderCount} files',
                Colors.blue,
              ),
              const SizedBox(height: 8),
              _buildStorageItem(
                '📥 Receiver Storage',
                _storageInfo!.formattedReceiverSize,
                '${_storageInfo!.receiverCount} files',
                Colors.green,
              ),
              const SizedBox(height: 8),
              _buildStorageItem(
                '📊 Total Storage',
                _storageInfo!.formattedTotalSize,
                '${_storageInfo!.totalCount} files',
                Colors.purple,
              ),
            ] else
              const Text('Loading storage information...'),
          ],
        ),
      ),
    );
  }

  Widget _buildStorageItem(
    String label,
    String size,
    String count,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(fontWeight: FontWeight.w600, color: color),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                size,
                style: TextStyle(fontWeight: FontWeight.bold, color: color),
              ),
              Text(
                count,
                style: TextStyle(fontSize: 12, color: color.withOpacity(0.7)),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '⚙️ Actions',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _clearCache,
                    icon: const Icon(Icons.clear_all),
                    label: const Text('Clear Cache'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _cleanupOldFiles,
                    icon: const Icon(Icons.cleaning_services),
                    label: const Text('Cleanup Old'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFlowDemoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '🔄 How It Works',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildFlowStep(
              '1',
              '📱 User sends images',
              'Stored in sender directory with encrypted names',
            ),
            _buildFlowStep(
              '2',
              '☁️ Upload to Firebase',
              'Temporary cloud storage for transfer',
            ),
            _buildFlowStep(
              '3',
              '📥 Receiver downloads',
              'Stored in receiver directory locally',
            ),
            _buildFlowStep(
              '4',
              '🗑️ Firebase cleanup',
              'Cloud files deleted after 5 minutes',
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Result: WhatsApp-like experience with local storage and privacy!',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFlowStep(String number, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.green[700],
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                Text(
                  description,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
