import 'package:flutter_test/flutter_test.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/utils/message_utils.dart';

void main() {
  group('Image Ordering Tests', () {
    test('Image group should maintain chronological order', () {
      // Create test messages in the order they would appear in Firestore (descending by timestamp)
      final now = DateTime.now();
      final messages = [
        // Third image (newest - appears first in Firestore result)
        Message(
          id: 'msg3',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image3.jpg',
          timestamp: now.add(Duration(seconds: 2)),
        ),
        // Second image
        Message(
          id: 'msg2',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image2.jpg',
          timestamp: now.add(Duration(seconds: 1)),
        ),
        // First image (oldest - appears last in Firestore result)
        Message(
          id: 'msg1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image1.jpg',
          timestamp: now,
        ),
      ];

      // Test with the middle message as target (simulating user tapping on second image)
      final targetMessage = messages[1]; // msg2
      final groupedMessages = MessageUtils.getImageGroupMessagesFromList(
        messages: messages,
        targetMessage: targetMessage,
      );

      // Verify that the grouped messages are in chronological order (oldest to newest)
      expect(groupedMessages.length, equals(3));
      expect(
        groupedMessages[0].id,
        equals('msg1'),
      ); // First image should be first
      expect(
        groupedMessages[1].id,
        equals('msg2'),
      ); // Second image should be second
      expect(
        groupedMessages[2].id,
        equals('msg3'),
      ); // Third image should be third

      // Verify URLs are in correct order
      final urls = groupedMessages.map((m) => m.mediaUrl).toList();
      expect(urls[0], equals('https://example.com/image1.jpg'));
      expect(urls[1], equals('https://example.com/image2.jpg'));
      expect(urls[2], equals('https://example.com/image3.jpg'));
    });

    test('Single image should return itself', () {
      final message = Message(
        id: 'msg1',
        senderId: 'user1',
        senderName: 'Test User',
        type: MessageType.image,
        mediaUrl: 'https://example.com/image1.jpg',
        timestamp: DateTime.now(),
      );

      final groupedMessages = MessageUtils.getImageGroupMessagesFromList(
        messages: [message],
        targetMessage: message,
      );

      expect(groupedMessages.length, equals(1));
      expect(groupedMessages[0].id, equals('msg1'));
    });

    test('Non-image message should return itself', () {
      final message = Message(
        id: 'msg1',
        senderId: 'user1',
        senderName: 'Test User',
        type: MessageType.text,
        text: 'Hello world',
        timestamp: DateTime.now(),
      );

      final groupedMessages = MessageUtils.getImageGroupMessagesFromList(
        messages: [message],
        targetMessage: message,
      );

      expect(groupedMessages.length, equals(1));
      expect(groupedMessages[0].id, equals('msg1'));
    });

    test('Images from different senders should not be grouped', () {
      final now = DateTime.now();
      final messages = [
        Message(
          id: 'msg2',
          senderId: 'user2',
          senderName: 'User 2',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image2.jpg',
          timestamp: now.add(Duration(seconds: 1)),
        ),
        Message(
          id: 'msg1',
          senderId: 'user1',
          senderName: 'User 1',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image1.jpg',
          timestamp: now,
        ),
      ];

      final targetMessage = messages[1]; // msg1 from user1
      final groupedMessages = MessageUtils.getImageGroupMessagesFromList(
        messages: messages,
        targetMessage: targetMessage,
      );

      // Should only return the target message since it's from a different sender
      expect(groupedMessages.length, equals(1));
      expect(groupedMessages[0].id, equals('msg1'));
    });

    test('Images sent more than 1 minute apart should not be grouped', () {
      final now = DateTime.now();
      final messages = [
        Message(
          id: 'msg2',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image2.jpg',
          timestamp: now.add(Duration(minutes: 2)), // 2 minutes later
        ),
        Message(
          id: 'msg1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image1.jpg',
          timestamp: now,
        ),
      ];

      final targetMessage = messages[1]; // msg1
      final groupedMessages = MessageUtils.getImageGroupMessagesFromList(
        messages: messages,
        targetMessage: targetMessage,
      );

      // Should only return the target message since the other is too far apart
      expect(groupedMessages.length, equals(1));
      expect(groupedMessages[0].id, equals('msg1'));
    });
  });
}
