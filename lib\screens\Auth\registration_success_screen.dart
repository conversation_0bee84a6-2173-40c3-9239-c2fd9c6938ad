import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/screens/Auth/login_screen.dart';
import 'package:mr_garments_mobile/screens/Auth/widgets/glassmorphic_card.dart';

class RegistrationSuccessScreen extends StatelessWidget {
  const RegistrationSuccessScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            GlassmorphicCard(
              child:  SingleChildScrollView(
                    child: Column(
                      children: [
                        Image.asset(
                          'assets/images/logo.png',
                          height: 140,
                          width: 140,
                        ),
                        const SizedBox(height: 20),
                        const Icon(
                          Icons.check_circle_outline,
                          color:  Color(0xFF00536B),
                          size: 70,
                        ),
                        const SizedBox(height: 20),
                        Text(
                          "Your registration data has been successfully submitted. Once our admin team approves it, you will be able to log in to the app. We will notify you upon approval.",
                          textAlign: TextAlign.center,
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 30),
                        ElevatedButton(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => LoginScreen(),
                              ),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const  Color(0xFF00536B),
                            minimumSize: const Size.fromHeight(50),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(14),
                            ),
                          ),
                          child: Text(
                            "Login Now",
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        SizedBox(height: 20),
                      ],
                    ),
                  ),
            ),
          ],
        )
        ),
    );
  }
}