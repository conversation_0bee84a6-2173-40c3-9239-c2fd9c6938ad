import 'dart:async';
import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/services/session_service.dart';

/// Service to manage user online status globally across the app
class OnlineStatusService with WidgetsBindingObserver {
  static final OnlineStatusService _instance = OnlineStatusService._internal();
  factory OnlineStatusService() => _instance;
  OnlineStatusService._internal();

  Timer? _heartbeatTimer;
  String? _currentUserId;
  bool _isInitialized = false;

  /// Initialize the online status service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    _currentUserId = (await SessionService.getUserId())?.toString();
    if (_currentUserId != null) {
      WidgetsBinding.instance.addObserver(this);
      await _setOnlineStatus(true);
      _startHeartbeat();
      _isInitialized = true;
    }
  }

  /// Dispose the online status service
  Future<void> dispose() async {
    if (!_isInitialized) return;
    
    WidgetsBinding.instance.removeObserver(this);
    _stopHeartbeat();
    await _setOnlineStatus(false);
    _isInitialized = false;
  }

  /// Handle app lifecycle changes
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.resumed:
        _setOnlineStatus(true);
        _startHeartbeat();
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        _setOnlineStatus(false);
        _stopHeartbeat();
        break;
      case AppLifecycleState.hidden:
        _setOnlineStatus(false);
        _stopHeartbeat();
        break;
    }
  }

  /// Start heartbeat to keep user online
  void _startHeartbeat() {
    _stopHeartbeat(); // Stop any existing timer
    _heartbeatTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _setOnlineStatus(true);
    });
  }

  /// Stop heartbeat timer
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// Set user online status
  Future<void> _setOnlineStatus(bool isOnline) async {
    if (_currentUserId != null) {
      try {
        await ChatService.updateUserOnlineStatus(_currentUserId!, isOnline);
      } catch (e) {
        // Silently handle errors
      }
    }
  }

  /// Update user ID (call when user changes)
  Future<void> updateUserId(String? userId) async {
    if (_currentUserId != userId) {
      // Set previous user offline
      if (_currentUserId != null) {
        await _setOnlineStatus(false);
      }
      
      _currentUserId = userId;
      
      // Set new user online
      if (_currentUserId != null) {
        await _setOnlineStatus(true);
        if (!_isInitialized) {
          await initialize();
        }
      }
    }
  }

  /// Force update online status
  Future<void> forceUpdateOnlineStatus(bool isOnline) async {
    await _setOnlineStatus(isOnline);
  }
}
