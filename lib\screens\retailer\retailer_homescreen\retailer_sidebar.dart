import 'package:flutter/material.dart';
// import 'package:mr_garments_mobile/screens/distributor/distributor_tabpage_view.dart';
// import 'package:mr_garments_mobile/screens/retailer/retailer_tabpage_view.dart';
import 'package:mr_garments_mobile/screens/common/generic_sidebar.dart';

class RetailerSidebar extends StatelessWidget {
  const RetailerSidebar({super.key});

  @override
  Widget build(BuildContext context) {
    return GenericSidebar(
      userType: 'retailer',
  //     customMenuItems: [
  //       SidebarMenuItem(
  //         icon: Icons.shopping_bag,
  //         title: "Retailers",
  //         subtitle: "View all retailers",
  //         onTap: () {
  //           Navigator.push(
  //             context,
  //             MaterialPageRoute(builder: (_) => const RetailerTabpageView()),
  //           );
  //         },
  //       ),
  //       SidebarMenuItem(
  //         icon: Icons.local_shipping,
  //         title: "Distributors",
  //         subtitle: "View all distributors",
  //         onTap: () {
  //           Navigator.push(
  //             context,
  //             MaterialPageRoute(builder: (_) => const DistributorTabpageView()),
  //           );
  //         },
  //       ),
  //     ],
    );
  }
}
