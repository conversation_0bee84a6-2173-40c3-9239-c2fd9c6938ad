import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/providers/generic_staff_provider.dart';
import 'package:mr_garments_mobile/screens/common/generic_add_staff_screen.dart';
import 'package:mr_garments_mobile/services/session_service.dart';

class GenericStaffManagementScreen extends ConsumerStatefulWidget {
  final String companyType; // 'manufacturer', 'retailer', 'distributor'

  const GenericStaffManagementScreen({super.key, required this.companyType});

  @override
  ConsumerState<GenericStaffManagementScreen> createState() =>
      _GenericStaffManagementScreenState();
}

class _GenericStaffManagementScreenState
    extends ConsumerState<GenericStaffManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounce;
  int? _companyId;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _initializeData();
    _searchController.addListener(_onSearchChanged);
  }

  Future<void> _initializeData() async {
    try {
      final userId = await SessionService.getUserId();
      if (userId != null) {
        setState(() => _companyId = userId);
        ref.read(genericStaffProvider.notifier).fetchStaffByCompany(userId);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error loading data: $e')));
      }
    }
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      setState(() {
        _searchQuery = _searchController.text.toLowerCase().trim();
      });
    });
  }

  void _refreshStaffList() {
    if (_companyId != null) {
      ref.read(genericStaffProvider.notifier).fetchStaffByCompany(_companyId!);
    }
  }

  String get _companyDisplayName {
    switch (widget.companyType.toLowerCase()) {
      case 'manufacturer':
        return 'Manufacturer';
      case 'retailer':
        return 'Retailer';
      case 'distributor':
        return 'Distributor';
      default:
        return 'Company';
    }
  }

  List<dynamic> _filterStaffMembers(List<dynamic> staffMembers) {
    if (_searchQuery.isEmpty) {
      return staffMembers;
    }

    return staffMembers.where((staff) {
      final name = staff['name']?.toString().toLowerCase() ?? '';
      final email = staff['email']?.toString().toLowerCase() ?? '';
      final mobile =
          staff['mobile']?.toString().toLowerCase() ??
          staff['mobile_number']?.toString().toLowerCase() ??
          '';

      return name.contains(_searchQuery) ||
          email.contains(_searchQuery) ||
          mobile.contains(_searchQuery);
    }).toList();
  }

  Widget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xFF005368),
      foregroundColor: Colors.white,
      elevation: 0,
      title: Text(
        "$_companyDisplayName Staff",
        style: GoogleFonts.poppins(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildSearchAndAddStaff() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: "Search staff members...",
                hintStyle: GoogleFonts.poppins(color: Colors.grey[600]),
                prefixIcon: const Icon(Icons.search, color: Colors.grey),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF005368)),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),
          const SizedBox(width: 10),
          IconButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFF2A738),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(40),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 10),
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => GenericAddStaffScreen(
                        companyType: widget.companyType,
                      ),
                ),
              ).then(
                (_) => _refreshStaffList(),
              ); // Refresh list after adding staff
            },
            icon: const Icon(Icons.add, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildStaffCard(Map<String, dynamic> staff) {
    final status = staff['status']?.toString().toLowerCase() ?? 'pending';

    // Determine status color and text based on status
    Color statusColor;
    String statusText;

    switch (status) {
      case 'approved':
        statusColor = Colors.green;
        statusText = 'Active';
        break;
      case 'deactivated':
        statusColor = Colors.red;
        statusText = 'Deactivated';
        break;
      default:
        statusColor = Colors.orange;
        statusText = 'Pending Approval';
        break;
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: const Color(0xFF005368),
                  child: Text(
                    staff['name']?.toString().substring(0, 1).toUpperCase() ??
                        'S',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        staff['name']?.toString() ?? 'Unknown',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF005368),
                        ),
                      ),
                      Text(
                        staff['email']?.toString() ?? '',
                        style: GoogleFonts.poppins(
                          fontSize: 12.5,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: statusColor.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    statusText,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: statusColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(LucideIcons.phone, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  staff['mobile_number']?.toString() ?? 'No phone',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const Spacer(),
                Icon(LucideIcons.calendar, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  'Joined: ${staff['joined_date']?.toString() ?? 'Unknown'}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final staffMembersAsync = ref.watch(genericStaffMembersProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(56),
        child: _buildAppBar(),
      ),
      body: Column(
        children: [
          _buildSearchAndAddStaff(),
          Expanded(
            child: staffMembersAsync.when(
              loading: () => const Center(child: CircularProgressIndicator()),
              error:
                  (error, _) => Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading staff',
                          style: GoogleFonts.poppins(
                            fontSize: 18,
                            color: Colors.red[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          error.toString(),
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.grey[500],
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _refreshStaffList,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
              data: (staffMembers) {
                final filteredStaff = _filterStaffMembers(staffMembers);

                if (filteredStaff.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          LucideIcons.users,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _searchQuery.isEmpty
                              ? 'No staff members found'
                              : 'No staff members match your search',
                          style: GoogleFonts.poppins(
                            fontSize: 18,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _searchQuery.isEmpty
                              ? 'Add your first staff member to get started'
                              : 'Try searching with a different term',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: filteredStaff.length,
                  itemBuilder: (context, index) {
                    return _buildStaffCard(filteredStaff[index]);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
