{"buildFiles": ["C:\\flutter_windows_3.24.4-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\BrainCave-1\\mr-garments-mobile\\android\\app\\.cxx\\RelWithDebInfo\\51325pu5\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\BrainCave-1\\mr-garments-mobile\\android\\app\\.cxx\\RelWithDebInfo\\51325pu5\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}