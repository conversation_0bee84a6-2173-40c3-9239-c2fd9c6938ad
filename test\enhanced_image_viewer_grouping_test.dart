import 'package:flutter_test/flutter_test.dart';
import 'package:mr_garments_mobile/models/message.dart';

void main() {
  group('Enhanced Image Viewer Grouping Tests', () {
    test('should filter out subsequent images in groups correctly', () {
      // Create test messages with timestamps within 1 minute
      final baseTime = DateTime.now();
      final messages = [
        // First group - 3 images from sender1 within 1 minute
        Message(
          id: '1',
          senderId: 'sender1',
          senderName: 'Sender 1',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image1.jpg',
          timestamp: baseTime,
        ),
        Message(
          id: '2',
          senderId: 'sender1',
          senderName: 'Sender 1',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image2.jpg',
          timestamp: baseTime.add(const Duration(seconds: 30)), // Within 1 minute
        ),
        Message(
          id: '3',
          senderId: 'sender1',
          senderName: 'Sender 1',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image3.jpg',
          timestamp: baseTime.add(const Duration(seconds: 45)), // Within 1 minute
        ),
        
        // Single image from sender2 (different sender)
        Message(
          id: '4',
          senderId: 'sender2',
          senderName: 'Sender 2',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image4.jpg',
          timestamp: baseTime.add(const Duration(minutes: 2)),
        ),
        
        // Second group - 2 images from sender1 after 5 minutes (new group)
        Message(
          id: '5',
          senderId: 'sender1',
          senderName: 'Sender 1',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image5.jpg',
          timestamp: baseTime.add(const Duration(minutes: 5)),
        ),
        Message(
          id: '6',
          senderId: 'sender1',
          senderName: 'Sender 1',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image6.jpg',
          timestamp: baseTime.add(const Duration(minutes: 5, seconds: 20)), // Within 1 minute of image5
        ),
      ];

      // Apply the same filtering logic as EnhancedImageViewerScreen
      final filteredMessages = _filterGroupedImages(messages);

      // Should only have 3 messages: first from each group
      expect(filteredMessages.length, equals(3));
      expect(filteredMessages[0].id, equals('1')); // First image from first group
      expect(filteredMessages[1].id, equals('4')); // Single image from sender2
      expect(filteredMessages[2].id, equals('5')); // First image from second group
    });

    test('should handle batch messages correctly', () {
      final baseTime = DateTime.now();
      final messages = [
        // Batch messages with same batchId should be grouped
        Message(
          id: '1',
          senderId: 'sender1',
          senderName: 'Sender 1',
          type: MessageType.image,
          mediaUrl: 'https://example.com/batch1.jpg',
          timestamp: baseTime,
          metadata: {'batchId': 'batch123'},
        ),
        Message(
          id: '2',
          senderId: 'sender1',
          senderName: 'Sender 1',
          type: MessageType.image,
          mediaUrl: 'https://example.com/batch2.jpg',
          timestamp: baseTime.add(const Duration(seconds: 10)),
          metadata: {'batchId': 'batch123'}, // Same batch
        ),
        Message(
          id: '3',
          senderId: 'sender1',
          senderName: 'Sender 1',
          type: MessageType.image,
          mediaUrl: 'https://example.com/batch3.jpg',
          timestamp: baseTime.add(const Duration(minutes: 3)), // Different time but same batch
          metadata: {'batchId': 'batch123'}, // Same batch
        ),
      ];

      final filteredMessages = _filterGroupedImages(messages);

      // Should only have 1 message (first from the batch)
      expect(filteredMessages.length, equals(1));
      expect(filteredMessages[0].id, equals('1'));
    });

    test('should not filter images from different senders', () {
      final baseTime = DateTime.now();
      final messages = [
        Message(
          id: '1',
          senderId: 'sender1',
          senderName: 'Sender 1',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image1.jpg',
          timestamp: baseTime,
        ),
        Message(
          id: '2',
          senderId: 'sender2', // Different sender
          senderName: 'Sender 2',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image2.jpg',
          timestamp: baseTime.add(const Duration(seconds: 30)), // Within 1 minute but different sender
        ),
      ];

      final filteredMessages = _filterGroupedImages(messages);

      // Should have both messages since they're from different senders
      expect(filteredMessages.length, equals(2));
      expect(filteredMessages[0].id, equals('1'));
      expect(filteredMessages[1].id, equals('2'));
    });

    test('should not filter images sent more than 1 minute apart', () {
      final baseTime = DateTime.now();
      final messages = [
        Message(
          id: '1',
          senderId: 'sender1',
          senderName: 'Sender 1',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image1.jpg',
          timestamp: baseTime,
        ),
        Message(
          id: '2',
          senderId: 'sender1', // Same sender
          senderName: 'Sender 1',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image2.jpg',
          timestamp: baseTime.add(const Duration(minutes: 2)), // More than 1 minute apart
        ),
      ];

      final filteredMessages = _filterGroupedImages(messages);

      // Should have both messages since they're more than 1 minute apart
      expect(filteredMessages.length, equals(2));
      expect(filteredMessages[0].id, equals('1'));
      expect(filteredMessages[1].id, equals('2'));
    });
  });
}

/// Helper function that mimics the filtering logic from EnhancedImageViewerScreen
List<Message> _filterGroupedImages(List<Message> allMessages) {
  return allMessages.where((message) {
    if (message.type != MessageType.image ||
        (message.mediaUrl == null && !message.hasLocalImage)) {
      return true;
    }

    // Find previous message
    final messageIndex = allMessages.indexOf(message);
    if (messageIndex > 0) {
      final prevMessage = allMessages[messageIndex - 1];
      final prevHasImageContent =
          prevMessage.mediaUrl != null || prevMessage.hasLocalImage;

      // If this is a subsequent image in a group, filter it out
      if (prevMessage.senderId == message.senderId &&
          prevMessage.type == MessageType.image &&
          prevHasImageContent &&
          (_isInSameBatch(message, prevMessage) ||
              message.timestamp
                      .difference(prevMessage.timestamp)
                      .inMinutes
                      .abs() <=
                  1)) {
        return false;
      }
    }
    return true;
  }).toList();
}

/// Check if two messages are in the same batch
bool _isInSameBatch(Message message1, Message message2) {
  final batch1 = message1.metadata?['batchId'];
  final batch2 = message2.metadata?['batchId'];
  return batch1 != null && batch2 != null && batch1 == batch2;
}
