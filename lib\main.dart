import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mr_garments_mobile/firebase_options.dart';
import 'package:mr_garments_mobile/screens/Auth/onboarding_screen.dart';
import 'package:mr_garments_mobile/screens/admin/admin_home_page.dart';
import 'package:mr_garments_mobile/screens/distributor/distributor_homescreen/distributor_home.dart';
import 'package:mr_garments_mobile/screens/manufacturer/Manufacturer_homescreen/manufacturer_home.dart';
import 'package:mr_garments_mobile/screens/retailer/retailer_homescreen/retailer_home.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/services/online_status_service.dart';
import 'package:mr_garments_mobile/services/notification_service.dart';
import 'package:mr_garments_mobile/services/whatsapp_local_storage_service.dart';

void main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Set up background message handler
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  // Check user session during splash screen time
  Widget initialScreen = const OnboardingScreen(); // Default screen

  try {
    final isLoggedIn = await SessionService.isLoggedIn();
    if (isLoggedIn) {
      // Initialize Firebase Auth for existing users
      try {
        // Switch user to ensure proper Firebase Auth mapping
        await ChatService.switchUser();

        // Initialize WhatsApp-like local storage
        await WhatsAppLocalStorageService.initialize();

        // Initialize online status service
        await OnlineStatusService().initialize();

        // Initialize notification service
        await NotificationService.initialize();

        // Ensure FCM token is saved for current user
        await _ensureFCMTokenSaved();

        // Clean up duplicate images (run periodically)
        _scheduleDuplicateCleanup();
      } catch (e) {
        // Chat initialization failed, but don't block app startup
      }

      final userRole = await SessionService.getUserRole();
      switch (userRole?.toLowerCase()) {
        case 'manufacturer':
          initialScreen = const ManufacturerHomeScreen();
          break;
        case 'retailer':
          initialScreen = const RetailerHomeScreen();
          break;
        case 'distributor':
          initialScreen = const DistributorHomeScreen();
          break;
        case 'admin':
          initialScreen = const AdminHomePage();
          break;
        case 'salesperson':
          // SalesPerson users navigate to admin home screen
          initialScreen = const AdminHomePage();
          break;
        case 'staff':
          // Staff members navigate to their company's home screen
          // We need to get the company type from user data
          final userData = await SessionService.getUserData();
          final companyType =
              userData?['company_type']?.toString().toLowerCase();
          switch (companyType) {
            case 'manufacturer':
              initialScreen = const ManufacturerHomeScreen();
              break;
            case 'retailer':
              initialScreen = const RetailerHomeScreen();
              break;
            case 'distributor':
              initialScreen = const DistributorHomeScreen();
              break;
            default:
              // Default to manufacturer if company type is unknown
              initialScreen = const ManufacturerHomeScreen();
          }
          break;
        default:
          // If role is unknown, clear session and go to onboarding
          await SessionService.clearSession();
          initialScreen = const OnboardingScreen();
      }
    }
  } catch (e) {
    // If there's any error, clear session and go to onboarding
    await SessionService.clearSession();
    initialScreen = const OnboardingScreen();
  }

  // Remove splash and run the app with determined initial screen
  await Future.delayed(Duration(seconds: 1));
  FlutterNativeSplash.remove();
  runApp(ProviderScope(child: MyApp(initialScreen: initialScreen)));
}

/// Ensure FCM token is saved for the current user
Future<void> _ensureFCMTokenSaved() async {
  try {
    final userId = await SessionService.getUserId();
    if (userId != null) {
      final token = await FirebaseMessaging.instance.getToken();
      if (token != null) {
        await ChatService.updateFCMToken(userId.toString(), token);
        debugPrint('✅ FCM token ensured for user $userId');
      }
    }
  } catch (e) {
    debugPrint('❌ Failed to ensure FCM token: $e');
  }
}

/// Schedule periodic cleanup of duplicate images
void _scheduleDuplicateCleanup() {
  // Run cleanup after a delay to not block app startup
  Future.delayed(const Duration(seconds: 30), () async {
    try {
      await WhatsAppLocalStorageService.cleanupDuplicateImages();
    } catch (e) {
      debugPrint('❌ Failed to cleanup duplicate images: $e');
    }
  });
}

const Color kSeedColor = Color(0xFF005368);

class MyApp extends StatelessWidget {
  final Widget initialScreen;

  const MyApp({super.key, required this.initialScreen});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'MR_Garments',
      debugShowCheckedModeBanner: false,
      navigatorKey: NavigatorService.navigatorKey,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: kSeedColor,
          brightness: Brightness.light,
        ),
        useMaterial3: true,
      ),
      darkTheme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: kSeedColor,
          brightness: Brightness.dark,
        ),
        useMaterial3: true,
      ),
      themeMode: ThemeMode.system,
      home: initialScreen,
    );
  }
}
