import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import '../../../models/message.dart';
import '../../../services/receiver_download_service.dart';
import '../../../utils/enhanced_image_utils.dart';

/// Widget for displaying receiver images with download option
class ReceiverImageWidget extends ConsumerStatefulWidget {
  final Message message;
  final String chatId;
  final double maxWidth;
  final double maxHeight;
  final VoidCallback? onTap;

  const ReceiverImageWidget({
    super.key,
    required this.message,
    required this.chatId,
    required this.maxWidth,
    required this.maxHeight,
    this.onTap,
  });

  @override
  ConsumerState<ReceiverImageWidget> createState() =>
      _ReceiverImageWidgetState();
}

class _ReceiverImageWidgetState extends ConsumerState<ReceiverImageWidget> {
  bool _isDownloading = false;
  double _downloadProgress = 0.0;

  @override
  void initState() {
    super.initState();
    _checkDownloadStatus();
  }

  Future<void> _checkDownloadStatus() async {
    final status = await ReceiverDownloadService.getDownloadStatus(
      widget.message,
    );
    if (mounted) {
      ref.read(downloadStatusProvider(widget.message.id).notifier).state =
          status;
    }
  }

  Future<void> _handleDownload() async {
    if (_isDownloading) return;

    final shouldDownload = await ReceiverDownloadService.showDownloadDialog(
      context: context,
      message: widget.message,
      chatId: widget.chatId,
    );

    if (!shouldDownload || !mounted) return;

    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
    });

    final success = await ReceiverDownloadService.downloadImageForReceiver(
      messageId: widget.message.id,
      chatId: widget.chatId,
      downloadUrl: widget.message.mediaUrl!,
      senderId: widget.message.senderId,
      onProgress: (progress) {
        if (mounted) {
          setState(() {
            _downloadProgress = progress;
          });
        }
      },
    );

    if (mounted) {
      setState(() {
        _isDownloading = false;
      });

      if (success) {
        ref.read(downloadStatusProvider(widget.message.id).notifier).state =
            DownloadStatus.downloaded;

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Image downloaded successfully'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      } else {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to download image'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final downloadStatus = ref.watch(downloadStatusProvider(widget.message.id));

    return GestureDetector(
      onTap: () {
        // If image is downloaded, allow viewing
        if (downloadStatus == DownloadStatus.downloaded) {
          widget.onTap?.call();
        } else {
          // If not downloaded, trigger download
          _handleDownload();
        }
      },
      child: Container(
        width: widget.maxWidth,
        height: widget.maxHeight,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.grey[300],
        ),
        child: Stack(
          children: [
            // Background image or placeholder
            _buildImageContent(downloadStatus),

            // Download overlay
            if (downloadStatus == DownloadStatus.notDownloaded &&
                !_isDownloading)
              _buildDownloadOverlay(),

            // Download progress overlay
            if (_isDownloading) _buildDownloadProgressOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildImageContent(DownloadStatus status) {
    if (status == DownloadStatus.downloaded && widget.message.hasLocalImage) {
      // Show local image
      return ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: EnhancedImageUtils.buildChatImage(
          widget.message.localImagePath!,
          width: widget.maxWidth,
          height: widget.maxHeight,
          fit: BoxFit.cover,
          isLocalFile: true,
        ),
      );
    }

    // Show placeholder with thumbnail if available
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: widget.maxWidth,
        height: widget.maxHeight,
        color: Colors.grey[300],
        child:
            widget.message.thumbnailUrl != null
                ? EnhancedImageUtils.buildChatImage(
                  widget.message.thumbnailUrl!,
                  width: widget.maxWidth,
                  height: widget.maxHeight,
                  fit: BoxFit.cover,
                )
                : const Center(
                  child: Icon(LucideIcons.image, size: 48, color: Colors.grey),
                ),
      ),
    );
  }

  Widget _buildDownloadOverlay() {
    return Container(
      width: widget.maxWidth,
      height: widget.maxHeight,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.black.withValues(alpha: 0.6),
      ),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: const BoxDecoration(
                color: Color(0xFF005368),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                LucideIcons.download,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Tap to download',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDownloadProgressOverlay() {
    return Container(
      width: widget.maxWidth,
      height: widget.maxHeight,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.black.withValues(alpha: 0.6),
      ),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 48,
              height: 48,
              child: CircularProgressIndicator(
                value: _downloadProgress,
                strokeWidth: 3,
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                backgroundColor: Colors.white.withValues(alpha: 0.3),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${(_downloadProgress * 100).toInt()}%',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
