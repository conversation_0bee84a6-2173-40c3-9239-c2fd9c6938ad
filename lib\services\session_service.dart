// lib/services/session_service.dart
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';

class SessionService {
  // Keys for SharedPreferences
  static const String _authTokenKey = 'auth_token';
  static const String _userDataKey = 'user_data';
  static const String _isLoggedInKey = 'is_logged_in';

  /// Save user session data after successful login
  static Future<void> saveUserSession({
    required String token,
    required Map<String, dynamic> userData,
  }) async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setString(_authTokenKey, token);
    await prefs.setString(_userDataKey, jsonEncode(userData));
    await prefs.setBool(_isLoggedInKey, true);
  }

  /// Get the stored authentication token
  static Future<String?> getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_authTokenKey);
  }

  /// Get the stored user data
  static Future<Map<String, dynamic>?> getUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataString = prefs.getString(_userDataKey);

    if (userDataString != null) {
      try {
        return jsonDecode(userDataString) as Map<String, dynamic>;
      } catch (e) {
        // If there's an error parsing the JSON, clear the corrupted data
        await clearSession();
        return null;
      }
    }
    return null;
  }

  /// Check if user is currently logged in
  static Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    final isLoggedIn = prefs.getBool(_isLoggedInKey) ?? false;
    final token = prefs.getString(_authTokenKey);

    // User is logged in if both flag is true and token exists
    return isLoggedIn && token != null && token.isNotEmpty;
  }

  /// Get the user's account type (role)
  static Future<String?> getUserRole() async {
    final userData = await getUserData();
    return userData?['account_type']?.toString().toLowerCase();
  }

  /// Get the user's name
  static Future<String?> getUserName() async {
    final userData = await getUserData();
    return userData?['name']?.toString();
  }

  /// Get the user's email
  static Future<String?> getUserEmail() async {
    final userData = await getUserData();
    return userData?['email']?.toString();
  }

  /// Get the user's ID
  static Future<int?> getUserId() async {
    final userData = await getUserData();
    final id = userData?['id'];
    if (id is int) return id;
    if (id is String) return int.tryParse(id);
    return null;
  }

  /// Clear all session data (logout)
  static Future<void> clearSession() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.remove(_authTokenKey);
    await prefs.remove(_userDataKey);
    await prefs.remove(_isLoggedInKey);

    // Also clear Firebase Auth session for chat functionality
    try {
      await FirebaseAuth.instance.signOut();
    } catch (e) {
      // Silently handle Firebase cleanup errors
    }
  }

  /// Update user data (useful for profile updates)
  static Future<void> updateUserData(Map<String, dynamic> newUserData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userDataKey, jsonEncode(newUserData));
  }

  /// Check if the stored token is valid (basic check)
  static Future<bool> isTokenValid() async {
    final token = await getAuthToken();
    if (token == null || token.isEmpty) return false;

    // You can add more sophisticated token validation here
    // For example, checking token expiration, making a test API call, etc.
    return true;
  }

  /// Get complete session info
  static Future<Map<String, dynamic>?> getSessionInfo() async {
    if (!await isLoggedIn()) return null;

    final token = await getAuthToken();
    final userData = await getUserData();

    if (token != null && userData != null) {
      return {'token': token, 'user': userData, 'isLoggedIn': true};
    }

    return null;
  }
}
