import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mr_garments_mobile/services/brand_service.dart';

/// --- Brand State Class ---
class BrandState {
  final AsyncValue<List<Map<String, dynamic>>> brands;

  const BrandState({required this.brands});

  BrandState copyWith({AsyncValue<List<Map<String, dynamic>>>? brands}) {
    return BrandState(brands: brands ?? this.brands);
  }
}

/// --- Brand Notifier ---
class BrandNotifier extends StateNotifier<BrandState> {
  BrandNotifier() : super(const BrandState(brands: AsyncValue.loading())) {
    fetchBrands();
  }

  Future<void> fetchBrands() async {
    try {
      final data = await BrandService.fetchBrands();
      state = state.copyWith(brands: AsyncValue.data(data.reversed.toList()));
    } catch (e, st) {
      state = state.copyWith(
        brands: AsyncValue.error(Exception('Failed to load brands'), st),
      );
    }
  }

  Future<void> addBrandAndRefresh({
    required String brandName,
    required String manufacturerName,
    File? image,
  }) async {
    await BrandService.addBrand(
      brandName: brandName,
      manufacturerName: manufacturerName,
      imageFile: image,
    );
    await fetchBrands();
  }
}

/// --- Providers ---
final brandProvider = StateNotifierProvider<BrandNotifier, BrandState>(
  (ref) => BrandNotifier(),
);

/// Selected image file used when uploading a brand image
final selectedBrandImageProvider = StateProvider<File?>((ref) => null);

/// Brands under a specific category
final categoryBrandsProvider =
    FutureProvider.family<List<Map<String, dynamic>>, int>((
      ref,
      categoryId,
    ) async {
      return await BrandService.fetchBrandsByCategory(categoryId);
    });

/// Catalogs under a specific brand (used in ViewCatalogPage)
final brandCatalogsProvider =
    FutureProvider.family<List<Map<String, dynamic>>, int>((
      ref,
      brandId,
    ) async {
      return await BrandService.fetchCatalogsByBrand(brandId);
    });
