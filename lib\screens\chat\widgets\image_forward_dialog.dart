import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/models/chat.dart';
import 'package:mr_garments_mobile/models/chat_user.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';
import 'package:mr_garments_mobile/utils/enhanced_image_cache.dart';
import 'package:mr_garments_mobile/screens/chat/member_chat_inbox.dart';

class ImageForwardScreen extends StatefulWidget {
  final List<String> imageUrls;
  final List<Message>
  imageMessages; // The actual message objects for each image
  final String fromChatId;

  const ImageForwardScreen({
    Key? key,
    required this.imageUrls,
    required this.imageMessages,
    required this.fromChatId,
  }) : super(key: key);

  @override
  State<ImageForwardScreen> createState() => _ImageForwardScreenState();
}

class _ImageForwardScreenState extends State<ImageForwardScreen>
    with SingleTickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  List<Chat> availableIndividualChats = [];
  List<Chat> availableGroups = [];
  List<Chat> filteredIndividualChats = [];
  List<Chat> filteredGroups = [];
  List<ChatUser> availableUsers = [];
  List<ChatUser> filteredUsers = [];
  Set<String> selectedChatIds = {};
  Set<int> selectedImageIndices = {}; // Track selected images by index
  bool isLoading = true;
  bool isSearching = false;
  String? currentUserId;
  bool _selectAll = false;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    // Initially select all images
    selectedImageIndices = Set.from(
      List.generate(widget.imageUrls.length, (index) => index),
    );
    _selectAll = true;
    _loadChatsAndUsers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadChatsAndUsers() async {
    try {
      final userId = await SessionService.getUserId();
      currentUserId = userId?.toString();
      if (currentUserId == null) {
        if (mounted) {
          Navigator.pop(context);
          AppSnackbar.showError(context, 'User not logged in');
        }
        return;
      }

      // Load both existing chats and all available users
      await Future.wait([_loadExistingChats(), _loadAllUsers()]);

      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        AppSnackbar.showError(context, 'Failed to load data: ${e.toString()}');
      }
    }
  }

  Future<void> _loadExistingChats() async {
    try {
      // Get user's chats stream and take the first snapshot
      final chatsStream = ChatService.getUserChatsStream(currentUserId!);
      await for (final chats in chatsStream) {
        if (mounted) {
          final filteredChats =
              chats.where((chat) => chat.id != widget.fromChatId).toList();
          setState(() {
            // Split into individual chats and groups
            availableIndividualChats =
                filteredChats
                    .where((chat) => chat.type == ChatType.individual)
                    .toList();
            availableGroups =
                filteredChats
                    .where((chat) => chat.type == ChatType.group)
                    .toList();
            // Initialize filtered lists
            filteredIndividualChats = availableIndividualChats;
            filteredGroups = availableGroups;
          });
        }
        break; // Take only the first snapshot
      }
    } catch (e) {
      // Handle error silently, will be caught by parent method
      throw e;
    }
  }

  Future<void> _loadAllUsers() async {
    try {
      // Get all available users from the backend
      final users = await ChatService.getAllUsers();
      if (mounted) {
        setState(() {
          availableUsers = users;
          filteredUsers = users;
        });
      }
    } catch (e) {
      // Handle error silently, will be caught by parent method
      throw e;
    }
  }

  void _searchChats(String query) {
    if (query.isEmpty) {
      setState(() {
        filteredIndividualChats = availableIndividualChats;
        filteredGroups = availableGroups;
        filteredUsers = availableUsers;
        isSearching = false;
      });
      return;
    }

    setState(() {
      isSearching = true;
      // Filter individual chats
      filteredIndividualChats =
          availableIndividualChats
              .where(
                (chat) => chat
                    .getDisplayName(currentUserId!)
                    .toLowerCase()
                    .contains(query.toLowerCase()),
              )
              .toList();
      // Filter groups
      filteredGroups =
          availableGroups
              .where(
                (chat) => chat
                    .getDisplayName(currentUserId!)
                    .toLowerCase()
                    .contains(query.toLowerCase()),
              )
              .toList();
      // Filter users
      filteredUsers =
          availableUsers
              .where(
                (user) =>
                    user.name.toLowerCase().contains(query.toLowerCase()) ||
                    user.email.toLowerCase().contains(query.toLowerCase()),
              )
              .toList();
    });
  }

  void _toggleChatSelection(String chatId) {
    setState(() {
      if (selectedChatIds.contains(chatId)) {
        selectedChatIds.remove(chatId);
      } else {
        selectedChatIds.add(chatId);
      }
    });
  }

  void _toggleUserSelection(ChatUser user) async {
    try {
      // Check if there's already an existing chat with this user
      final existingChat = availableIndividualChats.firstWhere(
        (chat) => chat.memberIds.contains(user.id),
        orElse:
            () => Chat(
              id: '',
              memberIds: [],
              type: ChatType.individual,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              isActive: true,
            ),
      );

      String chatId;
      if (existingChat.id.isNotEmpty) {
        // Use existing chat
        chatId = existingChat.id;
      } else {
        // Create new chat
        chatId = await ChatService.createIndividualChat(user.id);
      }

      // Toggle selection
      setState(() {
        if (selectedChatIds.contains(chatId)) {
          selectedChatIds.remove(chatId);
        } else {
          selectedChatIds.add(chatId);
        }
      });
    } catch (e) {
      if (mounted) {
        AppSnackbar.showError(
          context,
          'Failed to select user: ${e.toString()}',
        );
      }
    }
  }

  void _toggleImageSelection(int index) {
    setState(() {
      if (selectedImageIndices.contains(index)) {
        selectedImageIndices.remove(index);
      } else {
        selectedImageIndices.add(index);
      }
      _selectAll = selectedImageIndices.length == widget.imageUrls.length;
    });
  }

  void _toggleSelectAllImages() {
    setState(() {
      if (_selectAll) {
        selectedImageIndices.clear();
        _selectAll = false;
      } else {
        selectedImageIndices = Set.from(
          List.generate(widget.imageUrls.length, (index) => index),
        );
        _selectAll = true;
      }
    });
  }

  Future<void> _forwardSelectedImages() async {
    if (selectedChatIds.isEmpty) {
      AppSnackbar.showInfo(context, 'Please select at least one recipient');
      return;
    }

    if (selectedImageIndices.isEmpty) {
      AppSnackbar.showInfo(context, 'Please select at least one image');
      return;
    }

    try {
      // Get the selected messages
      final selectedMessages =
          selectedImageIndices
              .map((index) => widget.imageMessages[index])
              .toList();

      // If only one chat is selected, navigate to that chat and forward there
      if (selectedChatIds.length == 1) {
        final targetChatId = selectedChatIds.first;

        // Close the forward dialog immediately
        Navigator.pop(context);

        // Navigate to the target chat
        await _navigateToChat(targetChatId);

        // Forward messages in the background with individual sending indicators
        _forwardMessagesInBackground(selectedMessages, [targetChatId]);

        // Show success message
        if (mounted) {
          AppSnackbar.showSuccess(
            context,
            '${selectedImageIndices.length} image${selectedImageIndices.length > 1 ? 's' : ''} forwarding...',
          );
        }
      } else {
        // Multiple chats selected - show brief loading and forward to all
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => const Center(
                child: CircularProgressIndicator(color: Color(0xFF005368)),
              ),
        );

        // Forward to all selected chats in background
        _forwardMessagesInBackground(
          selectedMessages,
          selectedChatIds.toList(),
        );

        // Close loading dialog quickly
        await Future.delayed(const Duration(milliseconds: 500));
        if (mounted) Navigator.pop(context);

        if (mounted) {
          Navigator.pop(context);
          final imageCount = selectedImageIndices.length;
          final chatCount = selectedChatIds.length;
          AppSnackbar.showSuccess(
            context,
            '$imageCount image${imageCount > 1 ? 's' : ''} forwarding to $chatCount chat${chatCount > 1 ? 's' : ''}...',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        AppSnackbar.showError(
          context,
          'Failed to forward images: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _navigateToChat(String chatId) async {
    try {
      // Get the chat details
      final chat = await ChatService.getChat(chatId);
      if (chat == null || !mounted) return;

      // Get current user ID to determine display name
      final currentUserId = await SessionService.getUserId();
      final currentUserIdStr = currentUserId?.toString() ?? '';

      // Get display name for the chat
      String chatName = 'Chat';
      if (chat.isGroup) {
        chatName = chat.groupName ?? 'Group Chat';
      } else {
        chatName = chat.getDisplayName(currentUserIdStr);
      }

      // Navigate to the individual chat screen
      if (mounted) {
        // Use Future.microtask to avoid navigator assertion error
        Future.microtask(() {
          if (mounted) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder:
                    (context) => MemberChatInbox(
                      chatId: chatId,
                      chatName: chatName,
                      isGroup: chat.isGroup,
                    ),
              ),
            );
          }
        });
      }
    } catch (e) {
      // Handle error silently or show a snackbar
      if (mounted) {
        AppSnackbar.showError(context, 'Failed to open chat');
      }
    }
  }

  void _forwardMessagesInBackground(
    List<Message> messages,
    List<String> chatIds,
  ) {
    // Forward messages asynchronously without blocking UI
    Future.microtask(() async {
      for (final message in messages) {
        try {
          await ChatService.forwardMessage(
            fromChatId: widget.fromChatId,
            messageId: message.id,
            toChatIds: chatIds,
          );
          // Small delay between forwards to show individual sending
          await Future.delayed(const Duration(milliseconds: 200));
        } catch (e) {
          // Handle error silently - message forwarding will show individual status
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(LucideIcons.arrowLeft, color: Color(0xFF005368)),
        ),
        title: Text(
          'Forward Images',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF005368),
          ),
        ),
        actions: [
          if (selectedChatIds.isNotEmpty && selectedImageIndices.isNotEmpty)
            TextButton(
              onPressed: _forwardSelectedImages,
              child: Text(
                'Forward (${selectedImageIndices.length})',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF005368),
                ),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // Image selection section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Select images to forward:',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[700],
                      ),
                    ),
                    TextButton.icon(
                      onPressed: _toggleSelectAllImages,
                      icon: Icon(
                        _selectAll
                            ? LucideIcons.checkSquare
                            : LucideIcons.square,
                        size: 16,
                        color: const Color(0xFF005368),
                      ),
                      label: Text(
                        _selectAll ? 'Deselect All' : 'Select All',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: const Color(0xFF005368),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                // Image grid
                Container(
                  height: 100,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: widget.imageUrls.length,
                    itemBuilder: (context, index) {
                      final isSelected = selectedImageIndices.contains(index);
                      return Container(
                        margin: const EdgeInsets.only(right: 8),
                        child: GestureDetector(
                          onTap: () => _toggleImageSelection(index),
                          child: Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color:
                                    isSelected
                                        ? const Color(0xFF005368)
                                        : Colors.grey[300]!,
                                width: isSelected ? 3 : 1,
                              ),
                            ),
                            child: Stack(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(6),
                                  child: EnhancedImageCache.buildGridImage(
                                    widget.imageUrls[index],
                                    width: 80,
                                    height: 80,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                if (isSelected)
                                  Positioned(
                                    top: 4,
                                    right: 4,
                                    child: Container(
                                      padding: const EdgeInsets.all(2),
                                      decoration: const BoxDecoration(
                                        color: Color(0xFF005368),
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        LucideIcons.check,
                                        color: Colors.white,
                                        size: 10,
                                      ),
                                    ),
                                  ),
                                if (!isSelected)
                                  Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(6),
                                      color: Colors.black.withValues(
                                        alpha: 0.3,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),

          // Search bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              onChanged: _searchChats,
              decoration: InputDecoration(
                hintText: 'Search chats...',
                hintStyle: GoogleFonts.poppins(color: Colors.grey[500]),
                prefixIcon: const Icon(
                  LucideIcons.search,
                  color: Color(0xFF005368),
                  size: 20,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF005368)),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),

          // Tab Bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(25),
            ),
            child: TabBar(
              controller: _tabController,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.grey[600],
              indicator: BoxDecoration(
                color: const Color(0xFF005368),
                borderRadius: BorderRadius.circular(25),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
              tabs: [
                Tab(
                  height: 45,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(LucideIcons.user, size: 18),
                        const SizedBox(width: 8),
                        Text(
                          'Individual',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Tab(
                  height: 45,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(LucideIcons.users, size: 18),
                        const SizedBox(width: 8),
                        Text(
                          'Groups',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Chat lists
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Individual Users Tab
                _buildUserList(filteredUsers),
                // Groups Tab
                _buildChatList(filteredGroups),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserList(List<ChatUser> users) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFF005368)),
      );
    }

    if (users.isEmpty) {
      return _buildEmptyState(
        icon: LucideIcons.users,
        title: isSearching ? 'No users found' : 'No users available',
        subtitle:
            isSearching ? 'Try a different search term' : 'No users to show',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        // Check if there's an existing chat with this user
        final existingChat = availableIndividualChats.firstWhere(
          (chat) => chat.memberIds.contains(user.id),
          orElse:
              () => Chat(
                id: '',
                memberIds: [],
                type: ChatType.individual,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
                isActive: true,
              ),
        );

        final chatId =
            existingChat.id.isNotEmpty ? existingChat.id : 'new_${user.id}';
        final isSelected =
            selectedChatIds.contains(chatId) ||
            selectedChatIds.any((id) => id.contains(user.id));

        return _buildUserItem(user, isSelected);
      },
    );
  }

  Widget _buildChatList(List<Chat> chats) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFF005368)),
      );
    }

    if (chats.isEmpty) {
      return _buildEmptyState(
        icon: LucideIcons.users,
        title: isSearching ? 'No chats found' : 'No chats available',
        subtitle:
            isSearching ? 'Try a different search term' : 'No chats to show',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: chats.length,
      itemBuilder: (context, index) {
        final chat = chats[index];
        final isSelected = selectedChatIds.contains(chat.id);
        return _buildChatItem(chat, isSelected);
      },
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey.withValues(alpha: 0.5)),
          const SizedBox(height: 16),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildUserItem(ChatUser user, bool isSelected) {
    return InkWell(
      onTap: () => _toggleUserSelection(user),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? const Color(0xFF005368).withValues(alpha: 0.1)
                  : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? const Color(0xFF005368) : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // User avatar
            CircleAvatar(
              radius: 24,
              backgroundColor: const Color(0xFF005368),
              backgroundImage:
                  user.profileImageUrl != null &&
                          user.profileImageUrl!.isNotEmpty
                      ? NetworkImage(user.profileImageUrl!)
                      : null,
              child:
                  user.profileImageUrl == null || user.profileImageUrl!.isEmpty
                      ? Icon(LucideIcons.user, color: Colors.white, size: 24)
                      : null,
            ),

            const SizedBox(width: 12),

            // User details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.name,
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF005368),
                    ),
                  ),
                  Text(
                    user.role.toUpperCase(),
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),

            // Selection indicator
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color:
                      isSelected ? const Color(0xFF005368) : Colors.grey[400]!,
                  width: 2,
                ),
                color:
                    isSelected ? const Color(0xFF005368) : Colors.transparent,
              ),
              child:
                  isSelected
                      ? const Icon(
                        LucideIcons.check,
                        size: 16,
                        color: Colors.white,
                      )
                      : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatItem(Chat chat, bool isSelected) {
    return InkWell(
      onTap: () => _toggleChatSelection(chat.id),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? const Color(0xFF005368).withValues(alpha: 0.1)
                  : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? const Color(0xFF005368) : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // Chat avatar
            CircleAvatar(
              radius: 24,
              backgroundColor: const Color(0xFF005368),
              backgroundImage:
                  chat.getDisplayImage(currentUserId!) != null
                      ? NetworkImage(chat.getDisplayImage(currentUserId!)!)
                      : null,
              child:
                  chat.getDisplayImage(currentUserId!) == null
                      ? Icon(
                        chat.type == ChatType.group
                            ? LucideIcons.users
                            : LucideIcons.user,
                        color: Colors.white,
                        size: 24,
                      )
                      : null,
            ),

            const SizedBox(width: 12),

            // Chat details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    chat.getDisplayName(currentUserId!),
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF005368),
                    ),
                  ),
                  if (chat.lastMessage != null)
                    Text(
                      _getMessagePreview(chat.lastMessage!),
                      style: GoogleFonts.poppins(
                        fontSize: 13,
                        color: Colors.grey[600],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),

            // Selection indicator
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color:
                      isSelected ? const Color(0xFF005368) : Colors.grey[400]!,
                  width: 2,
                ),
                color:
                    isSelected ? const Color(0xFF005368) : Colors.transparent,
              ),
              child:
                  isSelected
                      ? const Icon(
                        LucideIcons.check,
                        size: 16,
                        color: Colors.white,
                      )
                      : null,
            ),
          ],
        ),
      ),
    );
  }

  String _getMessagePreview(Message message) {
    switch (message.type) {
      case MessageType.text:
        return message.text ?? 'Text message';
      case MessageType.image:
        return '📷 Image';
      case MessageType.video:
        return '🎥 Video';
      case MessageType.audio:
        return '🎵 Audio';
      case MessageType.file:
        return '📄 ${message.fileName ?? 'File'}';
      case MessageType.location:
        return '📍 Location';
      case MessageType.contact:
        return '👤 Contact';
      case MessageType.order:
        return '🛍️ Order';
      case MessageType.catalog:
        return '📚 Catalog';
    }
  }
}

// Keep the old dialog as a legacy fallback if needed
class ImageForwardDialog extends StatelessWidget {
  final List<String> imageUrls;
  final List<Message> imageMessages;
  final String fromChatId;

  const ImageForwardDialog({
    Key? key,
    required this.imageUrls,
    required this.imageMessages,
    required this.fromChatId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Navigate to the full-screen version instead of showing dialog
    // Use Future.microtask to avoid navigator assertion error
    final navigator = Navigator.of(context);
    Future.microtask(() {
      navigator.pushReplacement(
        MaterialPageRoute(
          builder:
              (context) => ImageForwardScreen(
                imageUrls: imageUrls,
                imageMessages: imageMessages,
                fromChatId: fromChatId,
              ),
        ),
      );
    });
    return const SizedBox.shrink();
  }
}
