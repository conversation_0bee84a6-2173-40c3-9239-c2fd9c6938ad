import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/screens/order/view_order.dart';

class OrderList extends StatelessWidget {
  final String filterType; // All, New, Completed
  final List<Map<String, dynamic>> orders;
  const OrderList({super.key, required this.filterType, required this.orders});

  

  @override
  Widget build(BuildContext context) {
    // Filter orders by selected filter type
    List<Map<String, dynamic>> filteredOrders;
    if (filterType == 'New') {
      filteredOrders =
          orders.where((order) => order['status'] == 'Pending').toList();
    } else if (filterType == 'Completed') {
      filteredOrders =
          orders.where((order) => order['status'] == 'Completed').toList();
    } else {
      filteredOrders = orders;
    }

    return ListView.builder(
      itemCount: filteredOrders.length,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      itemBuilder: (context, index) {
        final order = filteredOrders[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.shade300,
                blurRadius: 5,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.all(12),
            title: Text(
              order['orderId'],
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF005368),
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text("Status: ", style: GoogleFonts.poppins(fontSize: 13)),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color:
                            order['status'] == 'Completed'
                                ? Colors.green
                                : Colors.red,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        order['status'],
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  "Customer: ${order['customer']}",
                  style: GoogleFonts.poppins(fontSize: 13),
                ),
                const SizedBox(height: 2),
                Text(
                  "Order Date: ${order['date']}",
                  style: GoogleFonts.poppins(fontSize: 13),
                ),
              ],
            ),
            onTap: () {
              //Navigate to View Order screen
              Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => ViewOrderScreen(order: order,)),
              );
            },
          ),
        );
      },
    );
  }
}
