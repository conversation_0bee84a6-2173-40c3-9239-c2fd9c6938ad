import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/services/image_preload_service.dart';

/// Smart image preloader widget that automatically preloads images for better UX
class SmartImagePreloader extends ConsumerStatefulWidget {
  final String chatId;
  final List<Message> messages;
  final Widget child;
  final bool enablePreloading;
  final Function(int loaded, int total)? onPreloadProgress;
  final VoidCallback? onPreloadComplete;

  const SmartImagePreloader({
    super.key,
    required this.chatId,
    required this.messages,
    required this.child,
    this.enablePreloading = true,
    this.onPreloadProgress,
    this.onPreloadComplete,
  });

  @override
  ConsumerState<SmartImagePreloader> createState() => _SmartImagePreloaderState();
}

class _SmartImagePreloaderState extends ConsumerState<SmartImagePreloader>
    with WidgetsBindingObserver {
  final ImagePreloadService _preloadService = ImagePreloadService();
  bool _hasPreloaded = false;
  bool _isPreloading = false;
  int _preloadedCount = 0;
  int _totalImages = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    
    // Start preloading after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startPreloading();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didUpdateWidget(SmartImagePreloader oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // If messages changed significantly, restart preloading
    if (widget.messages.length != oldWidget.messages.length ||
        widget.chatId != oldWidget.chatId) {
      _hasPreloaded = false;
      _startPreloading();
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    // Resume preloading when app becomes active
    if (state == AppLifecycleState.resumed && !_hasPreloaded) {
      _startPreloading();
    }
  }

  void _startPreloading() {
    if (!widget.enablePreloading || _hasPreloaded || _isPreloading) {
      return;
    }

    setState(() {
      _isPreloading = true;
      _preloadedCount = 0;
      _totalImages = widget.messages
          .where((m) => m.type == MessageType.image && m.mediaUrl != null)
          .length;
    });

    _preloadService.preloadChatImages(
      chatId: widget.chatId,
      messages: widget.messages,
      onProgress: (loaded, total) {
        if (mounted) {
          setState(() {
            _preloadedCount = loaded;
            _totalImages = total;
          });
          widget.onPreloadProgress?.call(loaded, total);
        }
      },
      onComplete: () {
        if (mounted) {
          setState(() {
            _isPreloading = false;
            _hasPreloaded = true;
          });
          widget.onPreloadComplete?.call();
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        
        // Preloading indicator
        if (_isPreloading && _totalImages > 0)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: _buildPreloadingIndicator(),
          ),
      ],
    );
  }

  Widget _buildPreloadingIndicator() {
    return Container(
      margin: const EdgeInsets.all(8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: const Color(0xFF005368).withOpacity(0.9),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // SizedBox(
          //   width: 12,
          //   height: 12,
          //   child: CircularProgressIndicator(
          //     strokeWidth: 2,
          //     valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
          //     value: _totalImages > 0 ? _preloadedCount / _totalImages : null,
          //   ),
          // ),
          const SizedBox(width: 8),
          Text(
            'Loading images... $_preloadedCount/$_totalImages',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

/// Provider for image preload service
final imagePreloadServiceProvider = Provider<ImagePreloadService>((ref) {
  return ImagePreloadService();
});

/// Provider for preload statistics
final preloadStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final service = ref.watch(imagePreloadServiceProvider);
  return service.getPreloadStats();
});

/// Smart image widget that handles its own preloading
class SmartCachedImage extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;
  final Widget? placeholder;
  final Widget? errorWidget;
  final bool preloadOnInit;

  const SmartCachedImage({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.onTap,
    this.placeholder,
    this.errorWidget,
    this.preloadOnInit = true,
  }) : super(key: key);

  @override
  State<SmartCachedImage> createState() => _SmartCachedImageState();
}

class _SmartCachedImageState extends State<SmartCachedImage> {
  bool _isPreloaded = false;

  @override
  void initState() {
    super.initState();
    if (widget.preloadOnInit) {
      _preloadImage();
    }
  }

  void _preloadImage() async {
    final service = ImagePreloadService();
    // Check if already cached first
    final imageCache = ImagePreloadService();
    
    // This is a simplified check - in real implementation you'd check the cache
    setState(() {
      _isPreloaded = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    // Use the enhanced image cache for display
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: widget.borderRadius,
      ),
      child: ClipRRect(
        borderRadius: widget.borderRadius ?? BorderRadius.zero,
        child: GestureDetector(
          onTap: widget.onTap,
          child: Image.network(
            widget.imageUrl,
            width: widget.width,
            height: widget.height,
            fit: widget.fit,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              
              return widget.placeholder ?? Container(
                width: widget.width,
                height: widget.height,
                color: Colors.grey[200],
                child: const Center(
                  // child: CircularProgressIndicator(
                  //   strokeWidth: 2,
                  //   valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF005368)),
                  // ),
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return widget.errorWidget ?? Container(
                width: widget.width,
                height: widget.height,
                color: Colors.grey[200],
                child: const Center(
                  child: Icon(Icons.error, color: Colors.grey),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}