import 'package:flutter/material.dart';

/// Centralized snackbar utility for consistent snackbar behavior across the app
class AppSnackbar {
  /// Default duration for snackbars (reduced from 4 seconds to 1.5 seconds)
  static const Duration _defaultDuration = Duration(milliseconds: 1500);

  /// Show a success snackbar with green background
  static void showSuccess(BuildContext context, String message) {
    _showSnackbar(
      context,
      message,
      backgroundColor: Colors.green.shade600,
      icon: Icons.check_circle_outline,
    );
  }

  /// Show an error snackbar with red background
  static void showError(BuildContext context, String message) {
    _showSnackbar(
      context,
      message,
      backgroundColor: Colors.red.shade600,
      icon: Icons.error_outline,
    );
  }

  /// Show a warning snackbar with orange background
  static void showWarning(BuildContext context, String message) {
    _showSnackbar(
      context,
      message,
      backgroundColor: Colors.orange.shade600,
      icon: Icons.warning_outlined,
    );
  }

  /// Show an info snackbar with blue background
  static void showInfo(BuildContext context, String message) {
    _showSnackbar(
      context,
      message,
      backgroundColor: Colors.blue.shade600,
      icon: Icons.info_outline,
    );
  }

  /// Show a basic snackbar with default styling
  static void show(BuildContext context, String message, {Duration? duration}) {
    _showSnackbar(
      context,
      message,
      duration: duration,
    );
  }

  /// Internal method to show snackbar with consistent styling
  static void _showSnackbar(
    BuildContext context,
    String message, {
    Color? backgroundColor,
    IconData? icon,
    Duration? duration,
  }) {
    // Clear any existing snackbars first
    ScaffoldMessenger.of(context).clearSnackBars();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
            ],
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor ?? Colors.grey.shade700,
        duration: duration ?? _defaultDuration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        margin: const EdgeInsets.all(16),
        elevation: 6,
      ),
    );
  }
}
