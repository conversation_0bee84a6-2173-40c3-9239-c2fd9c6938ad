import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:mr_garments_mobile/screens/chat/widgets/image_forward_dialog.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/utils/enhanced_image_cache.dart';
import 'package:mr_garments_mobile/providers/chat_provider.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class EnhancedImageViewerScreen extends ConsumerStatefulWidget {
  final String chatId;
  final String initialImageUrl;
  final String senderName;
  final String timestamp;
  final List<String>? specificGroupUrls; // For viewing specific group images
  final List<Message>?
  specificGroupMessages; // For viewing specific group messages

  const EnhancedImageViewerScreen({
    super.key,
    required this.chatId,
    required this.initialImageUrl,
    required this.senderName,
    required this.timestamp,
    this.specificGroupUrls,
    this.specificGroupMessages,
  });

  @override
  ConsumerState<EnhancedImageViewerScreen> createState() =>
      _EnhancedImageViewerScreenState();
}

class _EnhancedImageViewerScreenState
    extends ConsumerState<EnhancedImageViewerScreen> {
  late PageController _pageController;
  late int _currentIndex;
  bool _isGridView = false;
  bool _isPreloading = false;
  int _preloadedCount = 0;
  bool _isSelectionMode = false;
  bool _isLoading = true;
  final Set<int> _selectedIndices = {};

  // All images from the chat in chronological order
  List<String> _allImageUrls = [];
  List<Message> _allImageMessages = [];

  @override
  void initState() {
    super.initState();
    _currentIndex = 0;
    _pageController = PageController(initialPage: 0);

    // Check if we're viewing a specific group or all chat images
    if (widget.specificGroupUrls != null &&
        widget.specificGroupMessages != null) {
      _loadSpecificGroupImages();
    } else {
      _loadAllChatImages();
    }
  }

  /// Load images from a specific group
  void _loadSpecificGroupImages() {
    final urls = widget.specificGroupUrls!;
    final messages = widget.specificGroupMessages!;

    // Process image URLs asynchronously to check local file existence
    _processImageUrlsFromGroup(urls, messages);
    _preloadImages();
  }

  /// Process image URLs from a specific group
  Future<void> _processImageUrlsFromGroup(
    List<String> urls,
    List<Message> messages,
  ) async {
    final List<String> processedUrls = [];

    for (int i = 0; i < urls.length; i++) {
      final message = messages[i];
      String bestUrl = urls[i];

      // Check if local image exists and use it if available
      if (message.hasLocalImage) {
        final localFile = File(message.localImagePath!);
        if (await localFile.exists()) {
          bestUrl = message.localImagePath!;
          debugPrint('🎯 Using local image: ${message.localImagePath}');
        } else {
          debugPrint(
            '⚠️ Local image not found, using network: ${message.mediaUrl}',
          );
        }
      }

      processedUrls.add(bestUrl);
    }

    if (mounted) {
      setState(() {
        _allImageMessages = messages;
        _allImageUrls = processedUrls;

        // Find the initial index based on the provided image URL
        _currentIndex = _allImageUrls.indexOf(widget.initialImageUrl);
        if (_currentIndex == -1) {
          // Try to find by matching the message with the same mediaUrl
          for (int i = 0; i < messages.length; i++) {
            if (messages[i].mediaUrl == widget.initialImageUrl ||
                messages[i].localImagePath == widget.initialImageUrl) {
              _currentIndex = i;
              break;
            }
          }
        }
        if (_currentIndex == -1) _currentIndex = 0;

        // Recreate page controller with correct initial page
        _pageController.dispose();
        _pageController = PageController(initialPage: _currentIndex);

        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// Load all images from the chat conversation in chronological order
  /// Only loads the first image from each group (same as chat interface)
  void _loadAllChatImages() async {
    final messagesAsync = ref.read(messagesStreamProvider(widget.chatId));

    messagesAsync.when(
      data: (messages) {
        // Extract all image messages and sort them chronologically (oldest to newest)
        final allImageMessages =
            messages
                .where(
                  (message) =>
                      message.type == MessageType.image &&
                      message.mediaUrl != null &&
                      message.mediaUrl!.isNotEmpty,
                )
                .toList();

        // Sort by timestamp (oldest to newest) with stable tie-breaker using optional batchIndex
        allImageMessages.sort((a, b) {
          final t = a.timestamp.compareTo(b.timestamp);
          if (t != 0) return t;
          final ai =
              (a.metadata != null && a.metadata!['batchIndex'] is int)
                  ? (a.metadata!['batchIndex'] as int)
                  : 0;
          final bi =
              (b.metadata != null && b.metadata!['batchIndex'] is int)
                  ? (b.metadata!['batchIndex'] as int)
                  : 0;
          return ai.compareTo(bi);
        });

        // Filter out subsequent messages in image groups (same logic as chat interface)
        final filteredImageMessages = _filterGroupedImages(allImageMessages);

        // Process image URLs asynchronously to check local file existence
        _processImageUrls(filteredImageMessages);

        _preloadImages();
      },
      loading: () {
        // Show loading state
      },
      error: (error, stack) {
        AppSnackbar.showError(context, 'Failed to load chat images: $error');
      },
    );
  }

  /// Filter out subsequent images in groups (same logic as chat interface)
  List<Message> _filterGroupedImages(List<Message> allMessages) {
    return allMessages.where((message) {
      if (message.type != MessageType.image ||
          (message.mediaUrl == null && !message.hasLocalImage)) {
        return true;
      }

      // Find previous message
      final messageIndex = allMessages.indexOf(message);
      if (messageIndex > 0) {
        final prevMessage = allMessages[messageIndex - 1];
        final prevHasImageContent =
            prevMessage.mediaUrl != null || prevMessage.hasLocalImage;

        // If this is a subsequent image in a group, filter it out
        if (prevMessage.senderId == message.senderId &&
            prevMessage.type == MessageType.image &&
            prevHasImageContent &&
            (_isInSameBatch(message, prevMessage) ||
                message.timestamp
                        .difference(prevMessage.timestamp)
                        .inMinutes
                        .abs() <=
                    1)) {
          return false;
        }
      }
      return true;
    }).toList();
  }

  /// Check if two messages are in the same batch
  bool _isInSameBatch(Message message1, Message message2) {
    final batch1 = message1.metadata?['batchId'];
    final batch2 = message2.metadata?['batchId'];
    return batch1 != null && batch2 != null && batch1 == batch2;
  }

  /// Preload all images for smooth scrolling experience
  void _preloadImages() async {
    if (_allImageUrls.isEmpty) return;

    setState(() {
      _isPreloading = true;
      _preloadedCount = 0;
    });

    // Preload images with progress tracking
    await EnhancedImageCache.preloadImages(
      _allImageUrls,
      onProgress: (loaded, total) {
        if (mounted) {
          setState(() {
            _preloadedCount = loaded;
          });
        }
      },
      onComplete: () {
        if (mounted) {
          setState(() {
            _isPreloading = false;
          });
        }
      },
    );
  }

  void _toggleViewMode() {
    if (_isGridView) {
      // Switching from grid to fullscreen - recreate PageController with current index
      _pageController.dispose();
      _pageController = PageController(initialPage: _currentIndex);
    }

    setState(() {
      _isGridView = !_isGridView;
      if (!_isGridView) {
        _exitSelectionMode();
      }
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedIndices.clear();
    });
  }

  /// Process image URLs to check local file existence and use best available paths
  Future<void> _processImageUrls(List<Message> imageMessages) async {
    final List<String> processedUrls = [];

    for (final message in imageMessages) {
      String bestUrl = message.mediaUrl ?? '';

      // Check if local image exists and use it if available
      if (message.hasLocalImage) {
        final localFile = File(message.localImagePath!);
        if (await localFile.exists()) {
          bestUrl = message.localImagePath!;
          debugPrint('🎯 Using local image: ${message.localImagePath}');
        } else {
          debugPrint(
            '⚠️ Local image not found, using network: ${message.mediaUrl}',
          );
        }
      }

      processedUrls.add(bestUrl);
    }

    if (mounted) {
      setState(() {
        _allImageMessages = imageMessages;
        _allImageUrls = processedUrls;

        // Find the initial index based on the provided image URL
        // Try to match with both network URL and local path
        _currentIndex = _allImageUrls.indexOf(widget.initialImageUrl);
        if (_currentIndex == -1) {
          // Try to find by matching the message with the same mediaUrl
          for (int i = 0; i < imageMessages.length; i++) {
            if (imageMessages[i].mediaUrl == widget.initialImageUrl ||
                imageMessages[i].localImagePath == widget.initialImageUrl) {
              _currentIndex = i;
              break;
            }
          }
        }
        if (_currentIndex == -1) _currentIndex = 0;

        // Recreate page controller with correct initial page
        _pageController.dispose();
        _pageController = PageController(initialPage: _currentIndex);

        _isLoading = false;
      });
    }
  }

  void _toggleImageSelection(int index) {
    setState(() {
      if (_selectedIndices.contains(index)) {
        _selectedIndices.remove(index);
      } else {
        _selectedIndices.add(index);
      }

      // Auto-enable selection mode when selecting images
      if (_selectedIndices.isNotEmpty && !_isSelectionMode) {
        _isSelectionMode = true;
      }

      // Auto-disable selection mode when no images are selected
      if (_selectedIndices.isEmpty && _isSelectionMode) {
        _isSelectionMode = false;
      }
    });
  }

  void _onPageChanged(int index) {
    if (_currentIndex != index) {
      setState(() {
        _currentIndex = index;
      });
    }

    // Preload adjacent images for smoother experience
    _preloadAdjacentImages(index);
  }

  void _preloadAdjacentImages(int currentIndex) {
    final imagesToPreload = <String>[];

    // Preload next 3 images
    for (int i = 1; i <= 3; i++) {
      final nextIndex = currentIndex + i;
      if (nextIndex < _allImageUrls.length) {
        imagesToPreload.add(_allImageUrls[nextIndex]);
      }
    }

    // Preload previous 3 images
    for (int i = 1; i <= 3; i++) {
      final prevIndex = currentIndex - i;
      if (prevIndex >= 0) {
        imagesToPreload.add(_allImageUrls[prevIndex]);
      }
    }

    // Preload in background
    if (imagesToPreload.isNotEmpty) {
      EnhancedImageCache.preloadImages(imagesToPreload);
    }
  }

  void _openFullscreenImage(int index) {
    // First update the current index
    _currentIndex = index;

    // Recreate the page controller with the correct initial page
    _pageController.dispose();
    _pageController = PageController(initialPage: index);

    // Then update the UI state
    setState(() {
      _isGridView = false;
    });
  }

  void _showForwardDialog() async {
    if (_allImageMessages.isNotEmpty) {
      List<String> urlsToForward;
      List<Message> messagesToForward;

      if (_isSelectionMode && _selectedIndices.isNotEmpty) {
        // Forward only selected images
        urlsToForward =
            _selectedIndices.map((index) => _allImageUrls[index]).toList();
        messagesToForward =
            _selectedIndices.map((index) => _allImageMessages[index]).toList();
      } else {
        // Forward current image
        urlsToForward = [_allImageUrls[_currentIndex]];
        messagesToForward = [_allImageMessages[_currentIndex]];
      }

      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => ImageForwardScreen(
                imageUrls: urlsToForward,
                imageMessages: messagesToForward,
                fromChatId: widget.chatId,
              ),
        ),
      );

      if (result == true && mounted) {
        AppSnackbar.showSuccess(context, 'Images forwarded successfully');
      }
    }
  }

  void _deleteSelectedImages() async {
    if (_allImageMessages.isEmpty) return;

    List<Message> messagesToDelete;
    String confirmationText;

    if (_isSelectionMode && _selectedIndices.isNotEmpty) {
      // Delete selected images
      messagesToDelete =
          _selectedIndices.map((index) => _allImageMessages[index]).toList();
      confirmationText =
          'Are you sure you want to delete ${_selectedIndices.length} selected image${_selectedIndices.length > 1 ? 's' : ''}?';
    } else {
      // Delete current image
      messagesToDelete = [_allImageMessages[_currentIndex]];
      confirmationText = 'Are you sure you want to delete this image?';
    }

    // Show confirmation dialog
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Delete Image${messagesToDelete.length > 1 ? 's' : ''}',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
            content: Text(confirmationText, style: GoogleFonts.poppins()),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text(
                  'Cancel',
                  style: GoogleFonts.poppins(color: Colors.grey[600]),
                ),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: Text(
                  'Delete',
                  style: GoogleFonts.poppins(
                    color: Colors.red,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
    );

    if (shouldDelete == true && mounted) {
      try {
        // Delete messages using message provider
        final messageNotifier = ref.read(
          messageProvider(widget.chatId).notifier,
        );

        for (final message in messagesToDelete) {
          await messageNotifier.deleteMessage(message.id);
        }

        // Show success message
        if (mounted) {
          AppSnackbar.showSuccess(
            context,
            'Image${messagesToDelete.length > 1 ? 's' : ''} deleted successfully',
          );
        }

        // Exit selection mode if we were in it
        if (_isSelectionMode) {
          _exitSelectionMode();
        }

        // If we deleted the current image in fullscreen mode, navigate to previous/next
        if (!_isGridView && messagesToDelete.length == 1) {
          // Check if there are still images left
          if (_allImageUrls.length <= 1) {
            // No more images, go back
            if (mounted) {
              Navigator.pop(context);
            }
          } else {
            // Navigate to previous image if current was deleted
            if (_currentIndex >= _allImageUrls.length) {
              _currentIndex = _allImageUrls.length - 1;
            }
            _pageController.animateToPage(
              _currentIndex,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }
        }
      } catch (error) {
        if (mounted) {
          AppSnackbar.showError(
            context,
            'Failed to delete image${messagesToDelete.length > 1 ? 's' : ''}: $error',
          );
        }
      }
    }
  }

  void _selectAllImages() {
    setState(() {
      _selectedIndices.clear();
      _selectedIndices.addAll(
        List.generate(_allImageUrls.length, (index) => index),
      );
      _isSelectionMode = true;
    });
  }

  void _deselectAllImages() {
    setState(() {
      _selectedIndices.clear();
      _isSelectionMode = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    // Listen to messages stream to update images in real-time (only for all chat images mode)
    if (widget.specificGroupUrls == null &&
        widget.specificGroupMessages == null) {
      ref.listen(messagesStreamProvider(widget.chatId), (previous, next) {
        next.whenData((messages) {
          // Update images when new messages arrive
          final allImageMessages =
              messages
                  .where(
                    (message) =>
                        message.type == MessageType.image &&
                        message.mediaUrl != null &&
                        message.mediaUrl!.isNotEmpty,
                  )
                  .toList();

          // Sort by timestamp (oldest to newest) with stable tie-breaker using optional batchIndex
          allImageMessages.sort((a, b) {
            final t = a.timestamp.compareTo(b.timestamp);
            if (t != 0) return t;
            final ai =
                (a.metadata != null && a.metadata!['batchIndex'] is int)
                    ? (a.metadata!['batchIndex'] as int)
                    : 0;
            final bi =
                (b.metadata != null && b.metadata!['batchIndex'] is int)
                    ? (b.metadata!['batchIndex'] as int)
                    : 0;
            return ai.compareTo(bi);
          });

          // Filter out subsequent messages in image groups (same logic as chat interface)
          final filteredImageMessages = _filterGroupedImages(allImageMessages);

          if (mounted) {
            // Process image URLs asynchronously to check local file existence
            _processImageUrls(filteredImageMessages);
          }
        });
      });
    }

    if (_allImageUrls.isEmpty) {
      return Scaffold(
        backgroundColor: Colors.black,
        appBar: AppBar(
          backgroundColor: Colors.black,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text(
            'Chat Images',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        body: const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
      );
    }

    if (_isGridView) {
      return _buildGridView();
    } else {
      return _buildFullscreenView();
    }
  }

  Widget _buildFullscreenView() {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          '${_currentIndex + 1} of ${_allImageUrls.length}',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(LucideIcons.grid, color: Colors.white),
            onPressed: _toggleViewMode,
            tooltip: 'Grid View',
          ),
          IconButton(
            icon: const Icon(LucideIcons.trash2, color: Colors.red),
            onPressed: _deleteSelectedImages,
            tooltip: 'Delete',
          ),
          IconButton(
            icon: const Icon(LucideIcons.share, color: Colors.white),
            onPressed: _showForwardDialog,
            tooltip: 'Forward',
          ),
        ],
      ),
      body: Stack(
        children: [
          PhotoViewGallery.builder(
            scrollPhysics: const BouncingScrollPhysics(),
            pageController: _pageController,
            builder: (context, index) {
              return PhotoViewGalleryPageOptions(
                imageProvider: EnhancedImageCache.getSmartImageProvider(
                  _allImageUrls[index],
                ),
                initialScale: PhotoViewComputedScale.contained,
                minScale: PhotoViewComputedScale.contained,
                maxScale: PhotoViewComputedScale.covered * 2,
                heroAttributes: PhotoViewHeroAttributes(
                  tag: 'enhanced_${_allImageUrls[index]}_$index',
                ),
              );
            },
            itemCount: _allImageUrls.length,
            loadingBuilder:
                (context, event) => const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                ),
            onPageChanged: _onPageChanged,
          ),

          // Preloading indicator
          if (_isPreloading)
            Positioned(
              top: 100,
              left: 0,
              right: 0,
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Loading images... $_preloadedCount/${_allImageUrls.length}',
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Image info overlay
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.8),
                    Colors.transparent,
                  ],
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _allImageMessages[_currentIndex].senderName,
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatTimestamp(
                      _allImageMessages[_currentIndex].timestamp,
                    ),
                    style: GoogleFonts.poppins(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGridView() {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          _isSelectionMode
              ? '${_selectedIndices.length} selected'
              : _getScreenTitle(),
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          if (_isSelectionMode) ...[
            // Selection mode actions
            // Select All / Deselect All button
            IconButton(
              icon: Icon(
                _selectedIndices.length == _allImageUrls.length
                    ? LucideIcons.checkSquare
                    : LucideIcons.square,
                color: Colors.white,
              ),
              onPressed:
                  _selectedIndices.length == _allImageUrls.length
                      ? _deselectAllImages
                      : _selectAllImages,
              tooltip:
                  _selectedIndices.length == _allImageUrls.length
                      ? 'Deselect All'
                      : 'Select All',
            ),
            IconButton(
              icon: const Icon(LucideIcons.trash2, color: Colors.red),
              onPressed:
                  _selectedIndices.isNotEmpty ? _deleteSelectedImages : null,
              tooltip: 'Delete Selected',
            ),
            IconButton(
              icon: const Icon(LucideIcons.share, color: Colors.white),
              onPressed:
                  _selectedIndices.isNotEmpty ? _showForwardDialog : null,
              tooltip: 'Forward Selected',
            ),
            IconButton(
              icon: const Icon(LucideIcons.x, color: Colors.white),
              onPressed: _exitSelectionMode,
              tooltip: 'Cancel',
            ),
          ] else ...[
            // Normal mode actions
            IconButton(
              icon: const Icon(LucideIcons.image, color: Colors.white),
              onPressed: _toggleViewMode,
              tooltip: 'Fullscreen View',
            ),
            if (_isPreloading)
              Padding(
                padding: const EdgeInsets.only(right: 16),
                child: Center(
                  child: Text(
                    '$_preloadedCount/${_allImageUrls.length}',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
          ],
        ],
      ),
      body: GridView.builder(
        padding: const EdgeInsets.all(1),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 1,
          mainAxisSpacing: 1,
        ),
        itemCount: _allImageUrls.length,
        itemBuilder: (context, index) {
          final isSelected = _selectedIndices.contains(index);
          return GestureDetector(
            onTap: () {
              if (_isSelectionMode) {
                _toggleImageSelection(index);
              } else {
                _openFullscreenImage(index);
              }
            },
            onLongPress: () {
              if (!_isSelectionMode) {
                _toggleImageSelection(index);
              }
            },
            child: Stack(
              children: [
                Hero(
                  tag: 'grid_enhanced_${_allImageUrls[index]}',
                  child: Container(
                    decoration: BoxDecoration(
                      border:
                          isSelected
                              ? Border.all(
                                color: const Color(0xFF005368),
                                width: 3,
                              )
                              : null,
                    ),
                    child: EnhancedImageCache.buildSmartGridImage(
                      _allImageUrls[index],
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                if (isSelected)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: const BoxDecoration(
                        color: Color(0xFF005368),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        LucideIcons.check,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                if (_isSelectionMode && !isSelected)
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.3),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  String _getScreenTitle() {
    if (widget.specificGroupUrls != null &&
        widget.specificGroupMessages != null) {
      return 'Group Images (${_allImageUrls.length})';
    } else {
      return 'All Chat Images (${_allImageUrls.length})';
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays == 0) {
      // Today - show time
      final hour = timestamp.hour;
      final minute = timestamp.minute.toString().padLeft(2, '0');
      final period = hour >= 12 ? 'PM' : 'AM';
      final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
      return '$displayHour:$minute $period';
    } else if (difference.inDays == 1) {
      // Yesterday
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      // This week - show day name
      const days = [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday',
      ];
      return days[timestamp.weekday - 1];
    } else {
      // Older - show date
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}
