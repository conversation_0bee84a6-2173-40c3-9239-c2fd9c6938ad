import 'package:cloud_firestore/cloud_firestore.dart';

class ChatUser {
  final String id;
  final String name;
  final String email;
  final String? mobile;
  final String? profileImageUrl;
  final String role; // manufacturer, retailer, distributor, admin
  final String? fcmToken;
  final bool isOnline;
  final DateTime? lastSeen;
  final DateTime createdAt;
  final DateTime updatedAt;

  ChatUser({
    required this.id,
    required this.name,
    required this.email,
    this.mobile,
    this.profileImageUrl,
    required this.role,
    this.fcmToken,
    this.isOnline = false,
    this.lastSeen,
    required this.createdAt,
    required this.updatedAt,
  });

  // Convert ChatUser to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'mobile': mobile,
      'profileImageUrl': profileImageUrl,
      'role': role,
      'fcmToken': fcmToken,
      'isOnline': isOnline,
      'lastSeen': lastSeen?.millisecondsSinceEpoch,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  // Create ChatUser from Firestore document
  factory ChatUser.fromMap(Map<String, dynamic> map) {
    try {
      return ChatUser(
        id: map['id']?.toString() ?? '',
        name: map['name']?.toString() ?? '',
        email: map['email']?.toString() ?? '',
        mobile: map['mobile']?.toString(),
        profileImageUrl: map['profileImageUrl']?.toString(),
        role: map['role']?.toString() ?? 'user',
        fcmToken: map['fcmToken']?.toString(),
        isOnline: map['isOnline'] == true,
        lastSeen:
            map['lastSeen'] != null ? _parseDateTime(map['lastSeen']) : null,
        createdAt: _parseDateTime(map['createdAt']) ?? DateTime.now(),
        updatedAt: _parseDateTime(map['updatedAt']) ?? DateTime.now(),
      );
    } catch (e) {
      // If there's an error, create a default user with available data
      print('Error parsing ChatUser from map: $e');
      print('Map data: $map');
      return ChatUser(
        id: map['id']?.toString() ?? '',
        name: map['name']?.toString() ?? 'Unknown User',
        email: map['email']?.toString() ?? '',
        role: map['role']?.toString() ?? 'user',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }
  }

  // Helper method to parse DateTime from Firestore (handles both Timestamp and int)
  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;

    if (value is Timestamp) {
      return value.toDate();
    } else if (value is int) {
      return DateTime.fromMillisecondsSinceEpoch(value);
    } else if (value is String) {
      return DateTime.tryParse(value);
    }

    return null;
  }

  // Create ChatUser from Firestore DocumentSnapshot
  factory ChatUser.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ChatUser.fromMap(data);
  }

  // Copy with method for updating user data
  ChatUser copyWith({
    String? id,
    String? name,
    String? email,
    String? mobile,
    String? profileImageUrl,
    String? role,
    String? fcmToken,
    bool? isOnline,
    DateTime? lastSeen,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ChatUser(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      mobile: mobile ?? this.mobile,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      role: role ?? this.role,
      fcmToken: fcmToken ?? this.fcmToken,
      isOnline: isOnline ?? this.isOnline,
      lastSeen: lastSeen ?? this.lastSeen,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'ChatUser(id: $id, name: $name, email: $email, role: $role, isOnline: $isOnline)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatUser && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
