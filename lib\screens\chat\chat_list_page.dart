import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/models/chat.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/providers/chat_provider.dart';
import 'package:mr_garments_mobile/screens/chat/member_chat_inbox.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:intl/intl.dart';

// Utility to generate unique chat room ID for a user pair
String getChatRoomId(String userId1, String userId2) {
  final sortedIds = [userId1, userId2]..sort();
  return '${sortedIds[0]}_${sortedIds[1]}';
}

class ChatListPage extends ConsumerWidget {
  const ChatListPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final chatsAsync = ref.watch(individualChatsStreamProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      body: RefreshIndicator(
        onRefresh: () async {
          // Invalidate the provider to force refresh
          ref.invalidate(individualChatsStreamProvider);
          // Wait a bit for the refresh to complete
          await Future.delayed(const Duration(milliseconds: 500));
        },
        child: chatsAsync.when(
          data: (chats) {
            if (chats.isEmpty) {
              return _buildEmptyState();
            }
            return _buildChatList(chats, ref, context);
          },
          loading:
              () => const Center(
                child: CircularProgressIndicator(color: Color(0xFF005368)),
              ),
          error: (error, stack) => _buildDetailedErrorState(error.toString()),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            LucideIcons.messageSquare,
            size: 80,
            color: Colors.grey.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No chats yet',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start a conversation by tapping the + button',
            style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              LucideIcons.alertCircle,
              size: 80,
              color: Colors.red.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading chats',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: GoogleFonts.poppins(fontSize: 14, color: Colors.red[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatList(List<Chat> chats, WidgetRef ref, BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.only(top: 4, bottom: 8),
      itemCount: chats.length,
      itemBuilder: (context, index) {
        final chat = chats[index];
        return _buildChatItem(chat, ref, context);
      },
    );
  }

  Widget _buildChatItem(Chat chat, WidgetRef ref, BuildContext context) {
    return FutureBuilder<String?>(
      future: SessionService.getUserId().then((id) => id?.toString()),
      builder: (context, snapshot) {
        final currentUserId = snapshot.data ?? '';
        final displayName = chat.getDisplayName(currentUserId);
        final displayImage = chat.getDisplayImage(currentUserId);
        final unreadCount = chat.getUnreadCount(currentUserId);

        return InkWell(
          onTap: () async {
            final currentUserId = await SessionService.getUserId();
            final currentUserIdStr = currentUserId?.toString() ?? '';
            final otherUserId = chat.memberIds.firstWhere(
              (id) => id != currentUserIdStr,
              orElse: () => '',
            );
            final otherUserIdStr = otherUserId;
            final chatRoomId = getChatRoomId(currentUserIdStr, otherUserIdStr);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder:
                    (context) => MemberChatInbox(
                      chatId: chatRoomId,
                      chatName: displayName,
                      isGroup: chat.isGroup,
                    ),
              ),
            );
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            child: Row(
              children: [
                // Profile picture
                _buildProfilePicture(displayImage, chat.isGroup),
                const SizedBox(width: 12),
                // Chat details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              displayName,
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF005368),
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (chat.lastMessageTime != null)
                            Text(
                              _formatTime(chat.lastMessageTime!),
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              _getLastMessageText(chat),
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: Colors.grey[700],
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          if (unreadCount > 0)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: const BoxDecoration(
                                color: Color(0xFFF2A738),
                                shape: BoxShape.circle,
                              ),
                              child: Text(
                                unreadCount > 99
                                    ? '99+'
                                    : unreadCount.toString(),
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildProfilePicture(String? imageUrl, bool isGroup) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: const Color(0xFF005368).withOpacity(0.1),
        border: Border.all(
          color: const Color(0xFF005368).withOpacity(0.2),
          width: 1,
        ),
      ),
      child:
          imageUrl != null && imageUrl.isNotEmpty
              ? ClipOval(
                child: Image.network(
                  imageUrl,
                  width: 50,
                  height: 50,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildDefaultAvatar(isGroup);
                  },
                ),
              )
              : _buildDefaultAvatar(isGroup),
    );
  }

  Widget _buildDefaultAvatar(bool isGroup) {
    return Icon(
      isGroup ? LucideIcons.users : LucideIcons.user,
      color: const Color(0xFF005368),
      size: 24,
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return 'Yesterday';
      } else if (difference.inDays < 7) {
        return DateFormat('EEE').format(time); // Mon, Tue, etc.
      } else {
        return DateFormat('dd/MM').format(time); // 15/03
      }
    } else if (difference.inHours > 0) {
      return DateFormat('HH:mm').format(time); // 14:30
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else { // Less than a minute
      return 'Just now';
    }
  }

  String _getLastMessageText(Chat chat) {
    if (chat.lastMessage == null) {
      return 'No messages yet';
    }

    final message = chat.lastMessage!;
    switch (message.type) {
      case MessageType.text:
        return message.text ?? '';
      case MessageType.image:
        return '📷 Photo';
      case MessageType.file:
        return '📎 ${message.fileName ?? 'File'}';
      case MessageType.audio:
        return '🎵 Audio';
      case MessageType.video:
        return '🎥 Video';
      case MessageType.location:
        return '📍 Location';
      case MessageType.contact:
        return '👤 Contact';
      case MessageType.order:
        return '🛍️ Order';
      case MessageType.catalog:
        return '📋 Catalog';
    }
  }
}
