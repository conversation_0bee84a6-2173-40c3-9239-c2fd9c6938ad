import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/models/chat.dart';
import 'package:mr_garments_mobile/models/chat_user.dart';
import 'package:mr_garments_mobile/providers/chat_provider.dart';
import 'package:mr_garments_mobile/screens/chat/member_chat_inbox.dart';
import 'package:mr_garments_mobile/services/session_service.dart';

class ChatSearchDelegate extends SearchDelegate<String> {
  @override
  String get searchFieldLabel => 'Search chats and users...';

  @override
  ThemeData appBarTheme(BuildContext context) {
    return Theme.of(context).copyWith(
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF005368),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      inputDecorationTheme: const InputDecorationTheme(
        hintStyle: TextStyle(color: Colors.white70),
        border: InputBorder.none,
      ),
      textTheme: const TextTheme(
        titleLarge: TextStyle(color: Colors.white), // Search input text
      ),
    );
  }

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
          showSuggestions(context);
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () => close(context, ''),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults(context);
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    if (query.isEmpty) {
      return _buildRecentChats(context);
    }
    return _buildSearchResults(context);
  }

  Widget _buildRecentChats(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final chatsAsync = ref.watch(userChatsStreamProvider);

        return chatsAsync.when(
          data: (chats) {
            final recentChats = chats.take(5).toList();

            if (recentChats.isEmpty) {
              return _buildEmptyState('No recent chats');
            }

            return ListView.builder(
              itemCount: recentChats.length,
              itemBuilder: (context, index) {
                final chat = recentChats[index];
                return _buildChatItem(context, chat);
              },
            );
          },
          loading:
              () => const Center(
                child: CircularProgressIndicator(color: Color(0xFF005368)),
              ),
          error: (error, stack) => _buildEmptyState('Error loading chats'),
        );
      },
    );
  }

  Widget _buildSearchResults(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final chatsAsync = ref.watch(userChatsStreamProvider);
        final usersAsync = ref.watch(allUsersProvider);

        return chatsAsync.when(
          data: (chats) {
            final filteredChats =
                chats.where((chat) {
                  return chat
                          .getDisplayName('')
                          .toLowerCase()
                          .contains(query.toLowerCase()) ||
                      (chat.lastMessage?.text?.toLowerCase().contains(
                            query.toLowerCase(),
                          ) ??
                          false);
                }).toList();

            return usersAsync.when(
              data: (users) {
                final filteredUsers =
                    users.where((user) {
                      return user.name.toLowerCase().contains(
                            query.toLowerCase(),
                          ) ||
                          user.email.toLowerCase().contains(
                            query.toLowerCase(),
                          );
                    }).toList();

                if (filteredUsers.isEmpty && filteredChats.isEmpty) {
                  return _buildEmptyState('No results found');
                }

                return SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (filteredChats.isNotEmpty) ...[
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Text(
                            'Chats',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF005368),
                            ),
                          ),
                        ),
                        ...filteredChats.map(
                          (chat) => _buildChatItem(context, chat),
                        ),
                      ],
                      if (filteredUsers.isNotEmpty) ...[
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Text(
                            'Users',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF005368),
                            ),
                          ),
                        ),
                        ...filteredUsers.map(
                          (user) => _buildUserItem(context, user, ref),
                        ),
                      ],
                    ],
                  ),
                );
              },
              loading:
                  () => const Center(
                    child: CircularProgressIndicator(color: Color(0xFF005368)),
                  ),
              error: (error, stack) => _buildEmptyState('Error loading users'),
            );
          },
          loading:
              () => const Center(
                child: CircularProgressIndicator(color: Color(0xFF005368)),
              ),
          error: (error, stack) => _buildEmptyState('Error searching chats'),
        );
      },
    );
  }

  Widget _buildChatItem(BuildContext context, Chat chat) {
    return FutureBuilder<String?>(
      future: SessionService.getUserId().then((id) => id?.toString()),
      builder: (context, snapshot) {
        final currentUserId = snapshot.data ?? '';
        final displayName = chat.getDisplayName(currentUserId);
        final displayImage = chat.getDisplayImage(currentUserId);

        return ListTile(
          leading: _buildAvatar(displayImage, chat.isGroup),
          title: Text(
            displayName,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          subtitle:
              chat.lastMessage != null
                  ? Text(
                    chat.lastMessage!.text ?? 'Media message',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  )
                  : null,
          onTap: () {
            close(context, '');
            Navigator.push(
              context,
              MaterialPageRoute(
                builder:
                    (context) => MemberChatInbox(
                      chatId: chat.id,
                      chatName: displayName,
                      isGroup: chat.isGroup,
                    ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildUserItem(BuildContext context, ChatUser user, WidgetRef ref) {
    return ListTile(
      leading: _buildAvatar(user.profileImageUrl, false),
      title: Text(
        user.name,
        style: GoogleFonts.poppins(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
      ),
      subtitle: Text(
        user.email,
        style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[600]),
      ),
      trailing: Icon(
        LucideIcons.messageSquare,
        color: const Color(0xFF005368),
        size: 20,
      ),
      onTap: () async {
        // Store context references before async operations
        final navigator = Navigator.of(context);
        final scaffoldMessenger = ScaffoldMessenger.of(context);

        try {
          // Close search first
          close(context, '');

          print('Starting chat with user: ${user.name} (${user.id})');

          // Create individual chat with this user
          final chatNotifier = ref.read(chatProvider.notifier);
          final chatId = await chatNotifier.createIndividualChat(user.id);

          print('Chat created with ID: $chatId');

          if (chatId != null) {
            print('Navigating to MemberChatInbox...');
            // Navigate to individual chat
            navigator.push(
              MaterialPageRoute(
                builder:
                    (context) => MemberChatInbox(
                      chatId: chatId,
                      chatName: user.name,
                      isGroup: false,
                    ),
              ),
            );
            // print('Navigation completed');
          } else {
            // print('Chat ID is null, cannot navigate');
            // Check if there's an error in the provider
            final chatState = ref.read(chatProvider);
            // print('Chat provider error: ${chatState.error}');

            // Show error if chat creation failed
            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text(
                  'Failed to create chat: ${chatState.error ?? "Unknown error"}',
                ),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        } catch (e) {
          // print('Exception during chat creation: $e');
          // Show error if something went wrong
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('Error: ${e.toString()}'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
    );
  }

  Widget _buildAvatar(String? imageUrl, bool isGroup) {
    return CircleAvatar(
      radius: 20,
      backgroundColor: const Color(0xFF005368).withOpacity(0.1),
      backgroundImage:
          imageUrl != null && imageUrl.isNotEmpty
              ? NetworkImage(imageUrl)
              : null,
      child:
          imageUrl == null || imageUrl.isEmpty
              ? Icon(
                isGroup ? LucideIcons.users : LucideIcons.user,
                color: const Color(0xFF005368),
                size: 20,
              )
              : null,
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            LucideIcons.search,
            size: 64,
            color: Colors.grey.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: GoogleFonts.poppins(fontSize: 16, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }
}
