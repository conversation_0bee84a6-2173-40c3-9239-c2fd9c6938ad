import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/providers/manufacturer_provider.dart';
import 'package:mr_garments_mobile/screens/admin/admin_home_page.dart';
import 'package:mr_garments_mobile/screens/manufacturer/manufacturer_details_tab.dart';
import 'package:mr_garments_mobile/services/manufacturer_servive.dart';
import 'package:mr_garments_mobile/widgets/invoice_tab.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';
import 'package:mr_garments_mobile/screens/common/chat_functionality.dart';

class ManufacturerDetailsScreen extends StatefulWidget {
  final int manufacturerId;
  const ManufacturerDetailsScreen({super.key, required this.manufacturerId});

  @override
  State<ManufacturerDetailsScreen> createState() =>
      _ManufacturerDetailsScreenState();
}

class _ManufacturerDetailsScreenState extends State<ManufacturerDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xFF005368),
      elevation: 0,
      foregroundColor: Colors.white,
      title: Row(
        children: [
          Text(
            "Manufacturer Details",
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap:
                () => Navigator.pushAndRemoveUntil(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AdminHomePage(),
                  ),
                  (route) => false,
                ),
            child: const Icon(Icons.home, color: Colors.white),
          ),
        ],
      ),
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        tabs: [
          Tab(child: Text("Details", style: GoogleFonts.poppins())),
          Tab(child: Text("Invoice", style: GoogleFonts.poppins())),
        ],
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Consumer(
      builder: (context, ref, _) {
        final detailsAsync = ref.watch(
          manufacturerDetailsProvider(widget.manufacturerId),
        );

        return Container(
          padding: const EdgeInsets.all(12),
          color: Colors.white,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    detailsAsync.when(
                      loading: () => null,
                      error: (e, _) => null,
                      data: (details) {
                        // Convert manufacturer details to the format expected by chat functionality
                        final manufacturerData = {
                          'id': widget.manufacturerId.toString(),
                          'name':
                              details['contactPerson'] ??
                              details['companyName'] ??
                              'Manufacturer',
                          'email': details['email'] ?? '',
                          'company_name': details['companyName'] ?? '',
                          'contact_person_name': details['contactPerson'] ?? '',
                          'email_id': details['email'] ?? '',
                        };

                        ChatFunctionality.startChatWithEntity(
                          context,
                          manufacturerData,
                          'manufacturer',
                        );
                        return null;
                      },
                    );
                  },
                  icon: const Icon(
                    Icons.chat_bubble_outline,
                    color: Colors.white,
                  ),
                  label: Text(
                    "Chat",
                    style: GoogleFonts.poppins(color: Colors.white),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF005368),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Consumer(
                  builder:
                      (context, ref, _) => ElevatedButton.icon(
                        onPressed: () async {
                          try {
                            showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder:
                                  (_) => const Center(
                                    child: CircularProgressIndicator(),
                                  ),
                            );
                            // Deactivate manufacturer
                            await ManufacturerService.deactivateManufacturer(
                              widget.manufacturerId,
                            );
                            // Update the manufacturers list BEFORE navigating back
                            final notifier = ref.read(
                              manufacturersProvider.notifier,
                            );
                            await notifier.fetchManufacturers();
                            if (!context.mounted) return;
                            Navigator.pop(context);
                            // show success message
                            AppSnackbar.showSuccess(
                              context,
                              'Manufacturer deactivated successfully',
                            );
                            Navigator.pop(context);
                          } catch (e) {
                            Navigator.pop(context); // close loader/dialog
                            if (context.mounted) {
                              AppSnackbar.showError(
                                context,
                                'Error: ${e.toString()}',
                              );
                            }
                          }
                        },
                        icon: const Icon(
                          Icons.cancel_outlined,
                          color: Colors.white,
                        ),
                        label: Text(
                          "Deactivate",
                          style: GoogleFonts.poppins(color: Colors.white),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red.shade600,
                        ),
                      ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                ManufacturerDetailsTab(
                  manufacturerId: widget.manufacturerId,
                ), // Replaced with glassmorphic tab
                const InvoiceTab(),
              ],
            ),
          ),
          _buildBottomButtons(),
        ],
      ),
    );
  }
}
