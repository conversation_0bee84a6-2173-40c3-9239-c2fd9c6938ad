import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mr_garments_mobile/providers/catalog_provider.dart';
import 'package:mr_garments_mobile/providers/category_provider.dart';
import 'package:mr_garments_mobile/providers/manufacturer_provider.dart';
import 'package:mr_garments_mobile/providers/brand_provider.dart';
import 'package:mr_garments_mobile/screens/admin/admin_home_page.dart';
// import 'package:mr_garments_mobile/services/catalog_service.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';

class AddCatalogScreen extends ConsumerStatefulWidget {
  final List<String>? preSelectedImageUrls;

  const AddCatalogScreen({super.key, this.preSelectedImageUrls});

  @override
  ConsumerState<AddCatalogScreen> createState() => _AddCatalogScreenState();
}

class _AddCatalogScreenState extends ConsumerState<AddCatalogScreen> {
  final TextEditingController _catalogNumberController =
      TextEditingController();
  int? selectedCategoryId;
  int? selectedManufacturerId;
  int? selectedBrandId;
  String? selectedBrandName;
  bool _isSubmitting = false;
  final List<XFile> _selectedImages = [];

  // Add this method for brand dropdown
  Widget _buildBrandDropdown() {
    final brandsAsync = ref.watch(brandProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader("Select Brand *"),
        const SizedBox(height: 12),
        brandsAsync.brands.when(
          data:
              (brands) => Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: DropdownButtonFormField<int>(
                  value: selectedBrandId,
                  decoration: InputDecoration(
                    hintText: "Choose a brand",
                    hintStyle: GoogleFonts.poppins(
                      color: Colors.grey[500],
                      fontSize: 14,
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF005368),
                        width: 2,
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 18,
                      vertical: 16,
                    ),
                  ),
                  items:
                      brands.map((brand) {
                        return DropdownMenuItem<int>(
                          value: brand['id'],
                          child: Text(
                            brand['brandName'] ?? 'Unknown',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: const Color(0xFF333333),
                            ),
                          ),
                        );
                      }).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedBrandId = value;
                      // Find the selected brand name
                      final selectedBrand = brands.firstWhere(
                        (b) => b['id'] == value,
                      );
                      selectedBrandName = selectedBrand['brandName'];
                    });
                  },
                  validator:
                      (value) => value == null ? 'Please select a brand' : null,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: const Color(0xFF333333),
                  ),
                ),
              ),
          loading:
              () => Container(
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: const Color(0xFFE0E0E0)),
                ),
                child: const Center(
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
              ),
          error:
              (_, __) => Container(
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                alignment: Alignment.center,
                child: Text(
                  'Error loading brands',
                  style: GoogleFonts.poppins(color: Colors.red, fontSize: 14),
                ),
              ),
        ),
      ],
    );
  }

  // Add this method for category dropdown
  Widget _buildCategoryDropdown() {
    final categoriesAsync = ref.watch(categoryProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader("Select Category *"),
        const SizedBox(height: 12),
        categoriesAsync.categories.when(
          data:
              (categories) => Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: DropdownButtonFormField<int>(
                  value: selectedCategoryId,
                  decoration: InputDecoration(
                    hintText: "Choose a category",
                    hintStyle: GoogleFonts.poppins(
                      color: Colors.grey[500],
                      fontSize: 14,
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF005368),
                        width: 2,
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 18,
                      vertical: 16,
                    ),
                  ),
                  items:
                      categories.map((category) {
                        return DropdownMenuItem<int>(
                          value: category['id'],
                          child: Text(
                            category['categoryName'] ?? 'Unknown',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: const Color(0xFF333333),
                            ),
                          ),
                        );
                      }).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedCategoryId = value;
                    });
                  },
                  validator:
                      (value) =>
                          value == null ? 'Please select a category' : null,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: const Color(0xFF333333),
                  ),
                ),
              ),
          loading:
              () => Container(
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: const Color(0xFFE0E0E0)),
                ),
                child: const Center(
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
              ),
          error:
              (_, __) => Container(
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                alignment: Alignment.center,
                child: Text(
                  'Error loading categories',
                  style: GoogleFonts.poppins(color: Colors.red, fontSize: 14),
                ),
              ),
        ),
      ],
    );
  }

  // Add this method for manufacturer dropdown
  Widget _buildManufacturerDropdown() {
    final manufacturersAsync = ref.watch(manufacturersProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader("Select Manufacturer *"),
        const SizedBox(height: 12),
        manufacturersAsync.manufacturers.when(
          data: (manufacturers) {
            // Filter out deactivated manufacturers
            final activeManufacturers =
                manufacturers
                    .where((m) => m['status']?.toLowerCase() != 'deactivated')
                    .toList();

            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: DropdownButtonFormField<int>(
                value: selectedManufacturerId,
                decoration: InputDecoration(
                  hintText: "Choose a manufacturer",
                  hintStyle: GoogleFonts.poppins(
                    color: Colors.grey[500],
                    fontSize: 14,
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF005368),
                      width: 2,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 18,
                    vertical: 16,
                  ),
                ),
                items:
                    activeManufacturers.map((manufacturer) {
                      return DropdownMenuItem<int>(
                        value: manufacturer['id'],
                        child: Text(
                          manufacturer['name'] ?? 'Unknown',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: const Color(0xFF333333),
                          ),
                        ),
                      );
                    }).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedManufacturerId = value;
                  });
                },
                validator:
                    (value) =>
                        value == null ? 'Please select a manufacturer' : null,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: const Color(0xFF333333),
                ),
              ),
            );
          },
          loading:
              () => Container(
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: const Color(0xFFE0E0E0)),
                ),
                child: const Center(
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
              ),
          error:
              (_, __) => Container(
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                alignment: Alignment.center,
                child: Text(
                  'Error loading manufacturers',
                  style: GoogleFonts.poppins(color: Colors.red, fontSize: 14),
                ),
              ),
        ),
      ],
    );
  }

  @override
  void initState() {
    super.initState();

    // Load pre-selected images after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadPreSelectedImages();
    });
  }

  @override
  void dispose() {
    _catalogNumberController.dispose();
    super.dispose();
  }

  void _loadPreSelectedImages() async {
    // print(
    //   'Debug AddCatalog: preSelectedImageUrls = ${widget.preSelectedImageUrls}',
    // );
    // print(
    //   'Debug AddCatalog: preSelectedImageUrls length = ${widget.preSelectedImageUrls?.length}',
    // );

    if (widget.preSelectedImageUrls != null &&
        widget.preSelectedImageUrls!.isNotEmpty) {
      try {
        // print(
        //   'Debug AddCatalog: Starting to load ${widget.preSelectedImageUrls!.length} images',
        // );

        // Show loading indicator
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Loading ${widget.preSelectedImageUrls!.length} pre-selected images...',
              ),
              duration: const Duration(seconds: 2),
            ),
          );
        }

        final List<XFile> downloadedImages = [];

        for (int i = 0; i < widget.preSelectedImageUrls!.length; i++) {
          final imageUrl = widget.preSelectedImageUrls![i];
          // print('Debug AddCatalog: Processing image $i: $imageUrl');
          try {
            // Download image from URL
            final response = await http.get(Uri.parse(imageUrl));
            // print(
            //   'Debug AddCatalog: HTTP response status: ${response.statusCode}',
            // );
            if (response.statusCode == 200) {
              // Get temporary directory
              final tempDir = await getTemporaryDirectory();
              final fileName =
                  'chat_image_${DateTime.now().millisecondsSinceEpoch}_$i.jpg';
              final file = File('${tempDir.path}/$fileName');

              // Write image data to file
              await file.writeAsBytes(response.bodyBytes);
              // print('Debug AddCatalog: Image saved to: ${file.path}');

              // Create XFile from the downloaded file
              final xFile = XFile(file.path);
              downloadedImages.add(xFile);
              // print(
              //   'Debug AddCatalog: Added XFile to list. Total: ${downloadedImages.length}',
              // );
            } else {
              // print(
              //   'Debug AddCatalog: Failed to download image. Status: ${response.statusCode}',
              // );
            }
          } catch (e) {
            // print('Debug AddCatalog: Error downloading image $imageUrl: $e');
            // Continue with other images even if one fails
          }
        }

        // print(
        //   'Debug AddCatalog: Downloaded images count: ${downloadedImages.length}',
        // );
        // print(
        //   'Debug AddCatalog: Current _selectedImages count before adding: ${_selectedImages.length}',
        // );

        if (mounted && downloadedImages.isNotEmpty) {
          setState(() {
            _selectedImages.addAll(downloadedImages);
          });

          // print(
          //   'Debug AddCatalog: Current _selectedImages count after adding: ${_selectedImages.length}',
          // );

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '${downloadedImages.length} images loaded successfully!',
              ),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          print('Debug AddCatalog: No images to add or widget not mounted');
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error loading pre-selected images: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _submitCatalog() async {
    if (selectedBrandId == null ||
        selectedBrandName == null ||
        _catalogNumberController.text.trim().isEmpty ||
        selectedCategoryId == null ||
        selectedManufacturerId == null ||
        _selectedImages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            "Please fill all fields, select a brand, category, manufacturer, and upload at least one image.",
          ),
        ),
      );
      return;
    }
    setState(() => _isSubmitting = true);
    try {
      await ref
          .read(catalogProvider.notifier)
          .addCatalogAndRefresh(
            brandName: selectedBrandName!,
            catalogNumber: _catalogNumberController.text.trim(),
            categoryId: selectedCategoryId!,
            manufacturerId: selectedManufacturerId!,
            images: _selectedImages.map((x) => File(x.path)).toList(),
            ref: ref,
          );

      // Refresh brands list in case a new brand was created
      ref.read(brandProvider.notifier).fetchBrands();

      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Catalog created successfully')));
      Navigator.pop(context);
    } catch (e) {
      // Error creating catalog: $e

      String errorMessage = 'Failed to create catalog';
      if (e.toString().contains('Authentication')) {
        errorMessage = 'Please login again to continue';
      } else if (e.toString().contains('too large') ||
          e.toString().contains('2048 kilobytes') ||
          e.toString().contains('Maximum allowed is 2MB')) {
        errorMessage =
            'Images are too large. Please reduce image size to under 2MB';
      } else if (e.toString().contains('network') ||
          e.toString().contains('connection')) {
        errorMessage = 'Network error. Please check your connection';
      } else if (e.toString().contains('500')) {
        errorMessage = 'Server error. Please try again later';
      }

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    } finally {
      if (mounted) setState(() => _isSubmitting = false);
    }
  }

  Future<void> _pickImagesFromGallery() async {
    try {
      final picker = ImagePicker();
      final pickedFiles = await picker.pickMultiImage(); // ✅ pick multiple

      if (pickedFiles.isNotEmpty) {
        // Verify all files exist and are readable
        final validFiles = <XFile>[];
        for (final file in pickedFiles) {
          final fileObj = File(file.path);
          if (await fileObj.exists()) {
            final fileSize = await fileObj.length();
            // Check if file size exceeds 2MB limit
            if (fileSize > 2 * 1024 * 1024) {
              if (!mounted) return;
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Image "${file.name}" is too large (${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB). Maximum allowed is 2MB.',
                  ),
                  backgroundColor: Colors.orange,
                  duration: const Duration(seconds: 4),
                ),
              );
              continue; // Skip this file
            }
            validFiles.add(file);
          }
        }

        if (validFiles.isNotEmpty) {
          setState(() {
            _selectedImages.addAll(validFiles);
          });

          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("${validFiles.length} image(s) selected.")),
          );
        } else {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text("No valid images found")),
          );
        }
      }
    } catch (e) {
      // print("Error picking images from gallery: $e");
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text("Gallery error: $e")));
    }
  }

  Future<void> _pickImageFromCamera() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85, // Compress image to reduce size
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (pickedFile != null) {
        // Verify the file exists and is readable
        final file = File(pickedFile.path);
        if (await file.exists()) {
          final fileSize = await file.length();
          // Check if file size exceeds 2MB limit
          if (fileSize > 2 * 1024 * 1024) {
            if (!mounted) return;
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Captured image is too large (${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB). Maximum allowed is 2MB.',
                ),
                backgroundColor: Colors.orange,
                duration: const Duration(seconds: 4),
              ),
            );
            return; // Don't add the image
          }

          setState(() {
            _selectedImages.add(pickedFile);
          });

          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("Captured: ${pickedFile.name}")),
          );
        } else {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text("Failed to save camera image")),
          );
        }
      }
    } catch (e) {
      // print("Error picking image from camera: $e");
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text("Camera error: $e")));
    }
  }

  Widget _buildUploadOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE0E0E0)),
              borderRadius: BorderRadius.circular(12),
              color: Colors.white,
            ),
            padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: const Color(0xFF005368).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(icon, color: const Color(0xFF005368), size: 24),
                ),
                const SizedBox(width: 18),
                Expanded(
                  child: Text(
                    label,
                    style: GoogleFonts.poppins(
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF333333),
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[400],
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedImagesPreview() {
    if (_selectedImages.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Selected Images (${_selectedImages.length})",
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF005368),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 110,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _selectedImages.length,
            itemBuilder: (context, index) {
              return Container(
                margin: const EdgeInsets.only(right: 12),
                child: Stack(
                  children: [
                    Container(
                      width: 110,
                      height: 110,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                        image: DecorationImage(
                          image: FileImage(File(_selectedImages[index].path)),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    Positioned(
                      top: 6,
                      right: 6,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedImages.removeAt(index);
                          });
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.7),
                            shape: BoxShape.circle,
                          ),
                          padding: const EdgeInsets.all(6),
                          child: const Icon(
                            Icons.close,
                            size: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAppBar() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF005368), Color(0xFF007B8A)],
        ),
        boxShadow: [
          BoxShadow(color: Colors.black12, blurRadius: 8, offset: Offset(0, 2)),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    Icons.arrow_back_ios_new,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  "Create Catalog",
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    letterSpacing: 0.5,
                  ),
                ),
              ),

              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  onPressed: () {
                    Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(builder: (context) => AdminHomePage()),
                      (route) => false,
                    );
                  },
                  icon: const Icon(
                    Icons.home_rounded,
                    color: Colors.white,
                    size: 22,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        fontWeight: FontWeight.w600,
        fontSize: 16,
        color: const Color(0xFF005368),
      ),
    );
  }

  Widget _buildTextFormField({
    required TextEditingController controller,
    required String hintText,
    required Color fillColor,
    bool readOnly = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        readOnly: readOnly,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: GoogleFonts.poppins(color: Colors.grey[500], fontSize: 14),
          filled: true,
          fillColor: fillColor,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFF005368), width: 2),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 18,
            vertical: 16,
          ),
        ),
        style: GoogleFonts.poppins(
          fontSize: 14,
          color: const Color(0xFF333333),
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFF2A738).withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: _isSubmitting ? null : _submitCatalog,
        icon:
            _isSubmitting
                ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                )
                : const Icon(
                  Icons.add_circle_outline,
                  color: Colors.white,
                  size: 20,
                ),
        label: Text(
          _isSubmitting ? "Creating Catalog..." : "Add Catalog",
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFF2A738),
          disabledBackgroundColor: const Color(
            0xFFF2A738,
          ).withValues(alpha: 0.6),
          padding: const EdgeInsets.symmetric(vertical: 18),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: _buildAppBar(),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
        keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Brand Selection Section
            _buildBrandDropdown(),
            const SizedBox(height: 28),

            // Category Selection Section
            _buildCategoryDropdown(),
            const SizedBox(height: 28),

            // Manufacturer Selection Section
            _buildManufacturerDropdown(),
            const SizedBox(height: 28),

            // Catalog Number Section
            _buildSectionHeader("Catalog Number"),
            const SizedBox(height: 12),
            _buildTextFormField(
              controller: _catalogNumberController,
              hintText: "Enter catalog number",
              fillColor: Colors.white,
            ),
            const SizedBox(height: 32),

            // Upload Photos Section
            _buildSectionHeader("Upload Photos"),
            const SizedBox(height: 16),
            _buildUploadOption(
              icon: Icons.photo_library_outlined,
              label: "Select from Gallery",
              onTap: _pickImagesFromGallery,
            ),
            const SizedBox(height: 16),
            _buildUploadOption(
              icon: Icons.camera_alt_outlined,
              label: "Take a Photo",
              onTap: _pickImageFromCamera,
            ),
            const SizedBox(height: 20),
            _buildSelectedImagesPreview(),

            const SizedBox(height: 40),

            // Add Catalog button
            _buildSubmitButton(),

            // Add bottom padding for better scrolling experience
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}
