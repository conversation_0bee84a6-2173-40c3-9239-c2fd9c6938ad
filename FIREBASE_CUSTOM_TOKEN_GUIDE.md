# Firebase Custom Token Integration (Optional Enhancement)

## Overview
For enhanced security, you can implement custom token authentication where your Laravel backend generates Firebase custom tokens.

## Laravel Backend Implementation

### 1. Install Firebase Admin SDK
```bash
composer require kreait/firebase-php
```

### 2. Add Firebase Service Account Key
- Download service account key from Firebase Console
- Place it in `storage/app/firebase/` directory
- Add to `.env`:
```
FIREBASE_CREDENTIALS=storage/app/firebase/service-account-key.json
```

### 3. Create Firebase Service
```php
// app/Services/FirebaseService.php
<?php

namespace App\Services;

use Kreait\Firebase\Factory;
use Kreait\Firebase\Auth;

class FirebaseService
{
    private $auth;

    public function __construct()
    {
        $factory = (new Factory)->withServiceAccount(storage_path('app/firebase/service-account-key.json'));
        $this->auth = $factory->createAuth();
    }

    public function createCustomToken($userId, $claims = [])
    {
        return $this->auth->createCustomToken($userId, $claims);
    }
}
```

### 4. Add API Endpoint
```php
// routes/api.php
Route::middleware('auth:sanctum')->post('/firebase/token', function (Request $request) {
    $user = $request->user();
    $firebaseService = new \App\Services\FirebaseService();
    
    $customToken = $firebaseService->createCustomToken($user->id, [
        'role' => $user->account_type,
        'name' => $user->name,
        'email' => $user->email,
    ]);

    return response()->json(['token' => $customToken]);
});
```

## Flutter Implementation

### 1. Update ChatService
```dart
// In ChatService class
static Future<void> authenticateWithCustomToken() async {
  try {
    // Get custom token from Laravel backend
    final response = await http.post(
      Uri.parse('${AuthService.baseUrl}/firebase/token'),
      headers: await AuthService.getAuthHeaders(),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final customToken = data['token'];
      
      // Sign in with custom token
      await _auth.signInWithCustomToken(customToken);
    }
  } catch (e) {
    // Fallback to anonymous auth
    await _auth.signInAnonymously();
  }
}
```

### 2. Call During App Initialization
```dart
// In main.dart or wherever you initialize chat
await ChatService.authenticateWithCustomToken();
await ChatService.syncCurrentUserWithFirebase();
```

## Benefits
- ✅ Stronger authentication tied to Laravel sessions
- ✅ Custom claims for role-based access
- ✅ Better audit trail
- ✅ Centralized user management

## Note
The current anonymous auth solution is sufficient for most use cases. Only implement custom tokens if you need enhanced security or audit requirements.
