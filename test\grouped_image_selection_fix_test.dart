import 'package:flutter_test/flutter_test.dart';
import 'package:mr_garments_mobile/models/message.dart';

void main() {
  group('Grouped Image Selection Fix Tests', () {
    test('Should correctly count selected images in a group', () {
      final now = DateTime.now();
      
      // Create a group of 5 images sent within 1 minute
      final messages = [
        Message(
          id: 'img1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image1.jpg',
          timestamp: now,
        ),
        Message(
          id: 'img2',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image2.jpg',
          timestamp: now.add(const Duration(seconds: 10)),
        ),
        Message(
          id: 'img3',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image3.jpg',
          timestamp: now.add(const Duration(seconds: 20)),
        ),
        Message(
          id: 'img4',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image4.jpg',
          timestamp: now.add(const Duration(seconds: 30)),
        ),
        Message(
          id: 'img5',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image5.jpg',
          timestamp: now.add(const Duration(seconds: 40)),
        ),
      ];

      final targetMessage = messages[0];
      final groupMessages = _getImageGroupMessages(messages, targetMessage);

      // Should return all 5 images in the group
      expect(groupMessages.length, equals(5));
      
      // Simulate selection - all 5 message IDs should be selected
      final selectedMessageIds = <String>{};
      for (final msg in groupMessages) {
        selectedMessageIds.add(msg.id);
      }
      
      // The count should be 5, not 1
      expect(selectedMessageIds.length, equals(5));
      expect(selectedMessageIds.contains('img1'), isTrue);
      expect(selectedMessageIds.contains('img2'), isTrue);
      expect(selectedMessageIds.contains('img3'), isTrue);
      expect(selectedMessageIds.contains('img4'), isTrue);
      expect(selectedMessageIds.contains('img5'), isTrue);
    });

    test('Should handle mixed message types correctly', () {
      final now = DateTime.now();
      
      final messages = [
        Message(
          id: 'text1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.text,
          text: 'Hello',
          timestamp: now,
        ),
        Message(
          id: 'img1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image1.jpg',
          timestamp: now.add(const Duration(seconds: 10)),
        ),
        Message(
          id: 'img2',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image2.jpg',
          timestamp: now.add(const Duration(seconds: 20)),
        ),
      ];

      // Test text message selection (should only select itself)
      final textGroupMessages = _getImageGroupMessages(messages, messages[0]);
      expect(textGroupMessages.length, equals(1));
      expect(textGroupMessages[0].id, equals('text1'));

      // Test image message selection (should select both images)
      final imageGroupMessages = _getImageGroupMessages(messages, messages[1]);
      expect(imageGroupMessages.length, equals(2));
      expect(imageGroupMessages.any((m) => m.id == 'img1'), isTrue);
      expect(imageGroupMessages.any((m) => m.id == 'img2'), isTrue);
    });

    test('Should handle selection toggle correctly', () {
      final now = DateTime.now();
      
      final messages = [
        Message(
          id: 'img1',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image1.jpg',
          timestamp: now,
        ),
        Message(
          id: 'img2',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image2.jpg',
          timestamp: now.add(const Duration(seconds: 10)),
        ),
        Message(
          id: 'img3',
          senderId: 'user1',
          senderName: 'Test User',
          type: MessageType.image,
          mediaUrl: 'https://example.com/image3.jpg',
          timestamp: now.add(const Duration(seconds: 20)),
        ),
      ];

      final selectedMessageIds = <String>{};
      final targetMessage = messages[0];
      final groupMessages = _getImageGroupMessages(messages, targetMessage);

      // Simulate first selection (select entire group)
      final isAnySelected = groupMessages.any((msg) => selectedMessageIds.contains(msg.id));
      expect(isAnySelected, isFalse);

      // Select the group
      for (final msg in groupMessages) {
        selectedMessageIds.add(msg.id);
      }
      expect(selectedMessageIds.length, equals(3));

      // Simulate second tap (deselect entire group)
      final isAnySelectedAfter = groupMessages.any((msg) => selectedMessageIds.contains(msg.id));
      expect(isAnySelectedAfter, isTrue);

      // Deselect the group
      for (final msg in groupMessages) {
        selectedMessageIds.remove(msg.id);
      }
      expect(selectedMessageIds.length, equals(0));
    });
  });
}

// Helper function that mimics the logic from the chat screen
List<Message> _getImageGroupMessages(List<Message> messages, Message targetMessage) {
  if (targetMessage.type != MessageType.image) {
    return [targetMessage];
  }

  // Find messages that are part of the same image group
  // Images are grouped if they are from the same sender and sent within 1 minute
  final groupMessages = messages.where((msg) {
    return msg.type == MessageType.image &&
        msg.senderId == targetMessage.senderId &&
        msg.timestamp.difference(targetMessage.timestamp).inMinutes.abs() <= 1;
  }).toList();

  // Sort by timestamp to maintain order
  groupMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
  
  return groupMessages.isNotEmpty ? groupMessages : [targetMessage];
}
