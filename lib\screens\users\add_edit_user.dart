import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/providers/distributor_provider.dart';
import 'package:mr_garments_mobile/providers/manufacturer_provider.dart';
import 'package:mr_garments_mobile/providers/retailer_provider.dart';
import 'package:mr_garments_mobile/providers/user_provider.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class AddEditUser extends ConsumerStatefulWidget {
  final Map<String, dynamic>? user; // null → add, non-null → edit

  const AddEditUser({super.key, this.user});

  @override
  ConsumerState<AddEditUser> createState() => _AddEditUserState();
}

class _AddEditUserState extends ConsumerState<AddEditUser> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _mobileController = TextEditingController();
  final _emailController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _roleController = TextEditingController();

  bool get isEdit => widget.user != null;
  bool _obscurePassword = true;
  bool _isPasswordChanged = false;
  final List<String> _roles = [
    'Manufacturer',
    'Retailer',
    'Distributor',
    'Admin',
  ];
  String? _selectedRole;

  @override
  void initState() {
    super.initState();
    if (isEdit) {
      _nameController.text = widget.user!['name'] ?? '';
      _mobileController.text = widget.user!['mobile'] ?? '';
      _emailController.text = widget.user!['email'] ?? '';
      _usernameController.text = widget.user!['username'] ?? '';
      _passwordController.text = widget.user!['password'] ?? '';
      _roleController.text = widget.user!['role'] ?? '';

      // Get the user's role and ensure it's valid for the dropdown
      final userRole = (widget.user!['role'] ?? '').toString().capitalize();
      _selectedRole = _roles.contains(userRole) ? userRole : _roles.first;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _mobileController.dispose();
    _emailController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _roleController.dispose();
    super.dispose();
  }

  InputDecoration getDecoration(String label) => InputDecoration(
    labelText: label,
    filled: true,
    fillColor: Colors.grey.shade100,
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12),
      borderSide: BorderSide.none,
    ),
  );

  void _showSnack(String msg) => AppSnackbar.show(context, msg);

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) return;

    final user = {
      "name": _nameController.text.trim(),
      "username": _usernameController.text.trim(),
      "email": _emailController.text.trim(),
      "mobile": _mobileController.text.trim(),
      "role": _selectedRole ?? '',
    };

    if (!isEdit || _isPasswordChanged) {
      user["password"] = _passwordController.text.trim();
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => const Center(child: CircularProgressIndicator()),
    );
    try {
      if (isEdit) {
        await ref
            .read(usersProvider.notifier)
            .updateUser(widget.user!['id'], user);

        // Manually trigger refresh
        await ref.read(manufacturersProvider.notifier).fetchManufacturers();
        await ref.read(distributorsProvider.notifier).fetchDistributors();
        await ref.read(retailersProvider.notifier).fetchRetailers();
      } else {
        await ref.read(usersProvider.notifier).addUser(user);
      }
      if (!mounted) return;
      Navigator.pop(context);
      _showSnack(
        isEdit ? 'User updated successfully' : 'User added successfully',
      );
      Navigator.pop(context);
    } catch (e) {
      Navigator.pop(context);
      _showSnack('Error: ${e.toString()}');
    }
  }

  Future<void> _deactivate() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => const Center(child: CircularProgressIndicator()),
    );

    try {
      await ref.read(usersProvider.notifier).deactivateUser(widget.user!['id']);
      if (!mounted) return;
      Navigator.pop(context);
      _showSnack('User deactivated successfully');
      Navigator.pop(context);
    } catch (e) {
      Navigator.pop(context);
      _showSnack('Error: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) => Scaffold(
    backgroundColor: Colors.white,
    appBar: AppBar(
      backgroundColor: const Color(0xFF005368),
      foregroundColor: Colors.white,
      title: Text(
        isEdit ? 'Edit User' : 'Add User',
        style: GoogleFonts.poppins(color: Colors.white, fontSize: 18),
      ),
    ),
    body: SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            TextFormField(
              controller: _nameController,
              decoration: getDecoration('Full Name'),
              validator: (v) => v!.isEmpty ? 'Required' : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _mobileController,
              decoration: getDecoration('Mobile Number'),
              keyboardType: TextInputType.phone,
              validator:
                  (v) =>
                      v!.isEmpty || !RegExp(r'^\d{10}$').hasMatch(v)
                          ? 'Enter valid 10-digit number'
                          : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _emailController,
              decoration: getDecoration('Email ID'),
              keyboardType: TextInputType.emailAddress,
              validator:
                  (v) =>
                      v!.isEmpty || !RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(v)
                          ? 'Enter valid email'
                          : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _usernameController,
              decoration: getDecoration('Username'),
              validator: (v) => v!.isEmpty ? 'Required' : null,
            ),
            const SizedBox(height: 16),
            // if (!isEdit) ...[
            TextFormField(
              controller: _passwordController,
              decoration: getDecoration('Password').copyWith(
                suffixIcon: IconButton(
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                  icon: Icon(
                    _obscurePassword ? Icons.visibility_off : Icons.visibility,
                  ),
                ),
              ),
              obscureText: _obscurePassword,
              onChanged: (_) {
                if (isEdit) {
                  setState(() {
                    _isPasswordChanged = true;
                  });
                }
              },
              validator: (v) {
                if (!isEdit && (v == null || v.length < 6)) {
                  return 'Min. 6 characters required';
                }
                if (_isPasswordChanged && (v == null || v.length < 6)) {
                  return 'Min. 6 characters required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            // ],
            DropdownButtonFormField<String>(
              value: _selectedRole,
              items:
                  _roles.map((role) {
                    return DropdownMenuItem(value: role, child: Text(role));
                  }).toList(),
              onChanged: (value) => setState(() => _selectedRole = value),
              decoration: getDecoration('Job Role'),
              validator:
                  (value) => value == null ? 'Please select a role' : null,
            ),
            const SizedBox(height: 22),
            Row(
              children: [
                if (isEdit)
                  Expanded(
                    child: OutlinedButton.icon(
                      icon: const Icon(Icons.person_off, color: Colors.red),
                      label: const Text(
                        "Deactivate User",
                        style: TextStyle(color: Colors.red),
                      ),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Colors.red),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                      ),
                      onPressed: _deactivate,
                    ),
                  ),
                if (isEdit) const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.check_circle),
                    label: Text(isEdit ? "Update User" : "Add User"),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFF2A738),
                      padding: const EdgeInsets.symmetric(vertical: 14),
                    ),
                    onPressed: _submit,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ),
  );
}

extension StringCasingExtension on String {
  String capitalize() =>
      isEmpty ? '' : '${this[0].toUpperCase()}${substring(1).toLowerCase()}';
}
