package com.braincave.mrgarments

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.provider.MediaStore
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream

class MainActivity : FlutterActivity() {
    private val CHANNEL = "custom_gallery_picker"
    private val PICK_IMAGES_REQUEST = 1001
    private val PICK_IMAGE_REQUEST = 1002
    
    private var pendingResult: MethodChannel.Result? = null
    private var isMultipleImages = false
    private var maxImages = 100

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "pickMultipleImagesFromGallery" -> {
                    pendingResult = result
                    isMultipleImages = true
                    maxImages = call.argument<Int>("maxImages") ?: 100
                    openGalleryForMultipleImages()
                }
                "pickImageFromGallery" -> {
                    pendingResult = result
                    isMultipleImages = false
                    openGalleryForSingleImage()
                }
                else -> result.notImplemented()
            }
        }
    }

    private fun openGalleryForMultipleImages() {
        val baseIntent = Intent(Intent.ACTION_GET_CONTENT).apply {
            type = "image/*"
            addCategory(Intent.CATEGORY_OPENABLE)
            putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }

        val pm = packageManager
        val handlers = pm.queryIntentActivities(baseIntent, 0)

        val excludedPackages = setOf(
            "com.google.android.apps.photos",
            "com.google.android.documentsui",
            "com.android.documentsui",
            "com.google.android.providers.media.module",
            "com.google.android.apps.nbu.files",
            "com.google.android.apps.photos.picker",
            "com.android.providers.media.module",
            "com.google.android.documentsui.files",
            "com.google.android.documentsui.picker"
        )

        // First try to find manufacturer-specific gallery apps
        val preferred = handlers.filter { ri ->
            val pkg = ri.activityInfo.packageName
            val isExcluded = excludedPackages.contains(pkg)
            val isSystemApp = (ri.activityInfo.applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_SYSTEM) != 0
            val isGalleryApp = pkg.contains("gallery", ignoreCase = true) || 
                              pkg.contains("media", ignoreCase = true)
            
            !isExcluded && (isSystemApp && isGalleryApp)
        }

        if (preferred.isNotEmpty()) {
            val targetIntents = preferred.map { ri ->
                Intent(baseIntent).apply {
                    setPackage(ri.activityInfo.packageName)
                    setClassName(ri.activityInfo.packageName, ri.activityInfo.name)
                }
            }

            val first = targetIntents.first()
            val chooser = Intent.createChooser(first, "Select images").apply {
                putExtra(Intent.EXTRA_INITIAL_INTENTS, targetIntents.drop(1).toTypedArray())
            }
            startActivityForResult(chooser, PICK_IMAGES_REQUEST)
        } else {
            // Fallback to generic chooser
            val chooser = Intent.createChooser(baseIntent, "Select images")
            startActivityForResult(chooser, PICK_IMAGES_REQUEST)
        }
    }

    private fun openGalleryForSingleImage() {
        val baseIntent = Intent(Intent.ACTION_GET_CONTENT).apply {
            type = "image/*"
            addCategory(Intent.CATEGORY_OPENABLE)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
 
        val pm = packageManager
        val handlers = pm.queryIntentActivities(baseIntent, 0)
        val excludedPackages = setOf(
            "com.google.android.apps.photos",
            "com.google.android.documentsui",
            "com.android.documentsui",
            "com.google.android.providers.media.module",
            "com.google.android.apps.nbu.files",
            "com.google.android.apps.photos.picker",
            "com.android.providers.media.module",
            "com.google.android.documentsui.files",
            "com.google.android.documentsui.picker"
        )

        val preferred = handlers.filter { ri ->
            val pkg = ri.activityInfo.packageName
            val isExcluded = excludedPackages.contains(pkg)
            val isSystemApp = (ri.activityInfo.applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_SYSTEM) != 0
            val isGalleryApp = pkg.contains("gallery", ignoreCase = true) || 
                              pkg.contains("media", ignoreCase = true)
            
            !isExcluded && (isSystemApp && isGalleryApp)
        }

        if (preferred.isNotEmpty()) {
            val targetIntents = preferred.map { ri ->
                Intent(baseIntent).apply {
                    setPackage(ri.activityInfo.packageName)
                    setClassName(ri.activityInfo.packageName, ri.activityInfo.name)
                }
            }
            val first = targetIntents.first()
            val chooser = Intent.createChooser(first, "Select image").apply {
                putExtra(Intent.EXTRA_INITIAL_INTENTS, targetIntents.drop(1).toTypedArray())
            }
            startActivityForResult(chooser, PICK_IMAGE_REQUEST)
        } else {
            val chooser = Intent.createChooser(baseIntent, "Select image")
            startActivityForResult(chooser, PICK_IMAGE_REQUEST)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (resultCode == Activity.RESULT_OK && data != null) {
            when (requestCode) {
                PICK_IMAGES_REQUEST -> {
                    val clipData = data.clipData
                    if (clipData != null) {
                        // Multiple images selected
                        val imagePaths = mutableListOf<String>()
                        val itemCount = minOf(clipData.itemCount, maxImages)
                        
                        for (i in 0 until itemCount) {
                            val uri = clipData.getItemAt(i).uri
                            val path = getRealPathFromURI(uri)
                            if (path != null) {
                                imagePaths.add(path)
                            }
                        }
                        
                        pendingResult?.success(imagePaths)
                    } else {
                        // Single image selected in multiple mode
                        val uri = data.data
                        if (uri != null) {
                            val path = getRealPathFromURI(uri)
                            if (path != null) {
                                pendingResult?.success(listOf(path))
                            } else {
                                pendingResult?.error("ERROR", "Failed to get image path", null)
                            }
                        } else {
                            pendingResult?.error("ERROR", "No image selected", null)
                        }
                    }
                }
                PICK_IMAGE_REQUEST -> {
                    // Single image selected
                    val uri = data.data
                    if (uri != null) {
                        val path = getRealPathFromURI(uri)
                        if (path != null) {
                            pendingResult?.success(path)
                        } else {
                            pendingResult?.error("ERROR", "Failed to get image path", null)
                        }
                    } else {
                        pendingResult?.error("ERROR", "No image selected", null)
                    }
                }
            }
        } else {
            pendingResult?.error("CANCELLED", "User cancelled image selection", null)
        }
        
        pendingResult = null
    }

    private fun getRealPathFromURI(uri: Uri): String? {
        return try {
            val cursor = contentResolver.query(uri, null, null, null, null)
            cursor?.use {
                if (it.moveToFirst()) {
                    val columnIndex = it.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
                    it.getString(columnIndex)
                } else {
                    null
                }
            }
        } catch (e: Exception) {
            // Fallback: copy to cache directory
            copyUriToCache(uri)
        }
    }

    private fun copyUriToCache(uri: Uri): String? {
        return try {
            val inputStream: InputStream? = contentResolver.openInputStream(uri)
            val cacheDir = cacheDir
            val fileName = "temp_image_${System.currentTimeMillis()}.jpg"
            val file = File(cacheDir, fileName)
            
            inputStream?.use { input ->
                FileOutputStream(file).use { output ->
                    input.copyTo(output)
                }
            }
            
            file.absolutePath
        } catch (e: Exception) {
            null
        }
    }
}
