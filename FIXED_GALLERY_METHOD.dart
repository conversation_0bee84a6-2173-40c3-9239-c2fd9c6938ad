// // CORRECTED _pickImagesFromGallery method for member_chat_inbox.dart
// // Replace your existing _pickImagesFromGallery method with this:

// void _pickImagesFromGallery() async {
//   // Prevent multiple simultaneous gallery operations
//   if (_isSendingFromGallery) return;

//   try {
//     setState(() {
//       _isSendingFromGallery = true;
//     });

//     // Navigate directly to optimized image selection screen
//     // It will handle the image picking internally
//     if (!mounted) return;

//     Navigator.push(
//       context,
//       MaterialPageRoute(
//         builder: (context) => OptimizedImageSelectionScreen(
//           // Don't pass initialImages - let it pick images itself
//           onImagesSelected: (processedImages) {
//             if (processedImages.isNotEmpty) {
//               _sendOptimizedImages(processedImages);
//             }
//           },
//           maxImages: 30, // WhatsApp-like limit
//         ),
//       ),
//     );
//   } catch (e) {
//     if (mounted) {
//       AppSnackbar.showError(context, 'Error opening image selection: $e');
//     }
//   } finally {
//     if (mounted) {
//       setState(() {
//         _isSendingFromGallery = false;
//       });
//     }
//   }
// }