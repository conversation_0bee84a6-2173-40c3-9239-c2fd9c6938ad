import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/providers/brand_provider.dart';
import 'package:mr_garments_mobile/providers/manufacturer_provider.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class AddBrandScreen extends ConsumerStatefulWidget {
  const AddBrandScreen({super.key});

  @override
  ConsumerState<AddBrandScreen> createState() => _AddBrandScreenState();
}

class _AddBrandScreenState extends ConsumerState<AddBrandScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _brandNameController = TextEditingController();
  int? selectedManufacturerId;
  String? selectedManufacturerName;

  bool _isSubmitting = false;

  Future<void> _pickImage() async {
    final pickedFile = await ImagePicker().pickImage(
      source: ImageSource.gallery,
    );

    if (pickedFile != null) {
      ref.read(selectedBrandImageProvider.notifier).state = File(
        pickedFile.path,
      );
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isSubmitting = true);

    try {
      final imageFile = ref.read(selectedBrandImageProvider);
      await ref
          .read(brandProvider.notifier)
          .addBrandAndRefresh(
            brandName: _brandNameController.text.trim(),
            manufacturerName: selectedManufacturerName ?? '',
            image: imageFile,
          );

      if (!mounted) return;
      Navigator.pop(context, true); // Notify success
    } catch (e) {
      if (mounted) {
        AppSnackbar.showError(context, "Error: ${e.toString()}");
      }
    } finally {
      setState(() => _isSubmitting = false);
    }
  }

  Widget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xFF005368),
      automaticallyImplyLeading: false,
      title: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: const Icon(Icons.arrow_back, color: Colors.white),
          ),
          const SizedBox(width: 16),
          Text(
            "Add Brand",
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        fontWeight: FontWeight.w600,
        fontSize: 16,
        color: const Color(0xFF005368),
      ),
    );
  }

  Widget _buildTextFormField({
    required TextEditingController controller,
    required String hintText,
    required String? Function(String?) validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        validator: validator,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: GoogleFonts.poppins(color: Colors.grey[500], fontSize: 14),
          filled: true,
          fillColor: Colors.white,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFF005368), width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 18,
            vertical: 16,
          ),
        ),
        style: GoogleFonts.poppins(
          fontSize: 14,
          color: const Color(0xFF333333),
        ),
      ),
    );
  }

  Widget _buildManufacturerDropdown() {
    final manufacturersAsync = ref.watch(manufacturersProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader("Select Manufacturer *"),
        const SizedBox(height: 12),
        manufacturersAsync.manufacturers.when(
          data: (manufacturers) {
            // Filter out deactivated manufacturers
            final activeManufacturers =
                manufacturers
                    .where((m) => m['status']?.toLowerCase() != 'deactivated')
                    .toList();

            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: DropdownButtonFormField<int>(
                value: selectedManufacturerId,
                decoration: InputDecoration(
                  hintText: "Choose a manufacturer",
                  hintStyle: GoogleFonts.poppins(
                    color: Colors.grey[500],
                    fontSize: 14,
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF005368),
                      width: 2,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 18,
                    vertical: 16,
                  ),
                ),
                items:
                    activeManufacturers.map((manufacturer) {
                      return DropdownMenuItem<int>(
                        value: manufacturer['id'],
                        child: Text(
                          manufacturer['name'] ?? 'Unknown',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: const Color(0xFF333333),
                          ),
                        ),
                      );
                    }).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedManufacturerId = value;
                    // Find the selected manufacturer name
                    final selectedManufacturer = activeManufacturers.firstWhere(
                      (m) => m['id'] == value,
                    );
                    selectedManufacturerName = selectedManufacturer['name'];
                  });
                },
                validator:
                    (value) =>
                        value == null ? 'Please select a manufacturer' : null,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: const Color(0xFF333333),
                ),
              ),
            );
          },
          loading:
              () => Container(
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: const Color(0xFFE0E0E0)),
                ),
                child: const Center(
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
              ),
          error:
              (_, __) => Container(
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                alignment: Alignment.center,
                child: Text(
                  'Error loading manufacturers',
                  style: GoogleFonts.poppins(color: Colors.red, fontSize: 14),
                ),
              ),
        ),
      ],
    );
  }

  Widget _buildImageUploadSection(File? selectedImage) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader("Brand Image"),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _pickImage,
              borderRadius: BorderRadius.circular(12),
              child: Container(
                height: 180,
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(
                    color:
                        selectedImage != null
                            ? const Color(0xFF005368).withValues(alpha: 0.3)
                            : const Color(0xFFE0E0E0),
                    width: selectedImage != null ? 2 : 1,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white,
                  image:
                      selectedImage != null
                          ? DecorationImage(
                            image: FileImage(selectedImage),
                            fit: BoxFit.cover,
                          )
                          : null,
                ),
                child:
                    selectedImage == null
                        ? Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: const Color(
                                  0xFF005368,
                                ).withValues(alpha: 0.1),
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.add_a_photo,
                                size: 32,
                                color: Color(0xFF005368),
                              ),
                            ),
                            const SizedBox(height: 12),
                            Text(
                              "Tap to select brand image",
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              "JPG, PNG up to 10MB",
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.grey[500],
                              ),
                            ),
                          ],
                        )
                        : Stack(
                          children: [
                            Positioned(
                              top: 8,
                              right: 8,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0.7),
                                  shape: BoxShape.circle,
                                ),
                                padding: const EdgeInsets.all(6),
                                child: const Icon(
                                  Icons.edit,
                                  size: 16,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFF2A738).withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: _isSubmitting ? null : _submitForm,
        icon:
            _isSubmitting
                ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                )
                : const Icon(
                  Icons.add_circle_outline,
                  color: Colors.white,
                  size: 20,
                ),
        label: Text(
          _isSubmitting ? "Creating Brand..." : "Add Brand",
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFF2A738),
          disabledBackgroundColor: const Color(
            0xFFF2A738,
          ).withValues(alpha: 0.6),
          padding: const EdgeInsets.symmetric(vertical: 18),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final selectedImage = ref.watch(selectedBrandImageProvider);

    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: _buildAppBar(),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
        keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Brand Name Section
              _buildSectionHeader("Brand Name"),
              const SizedBox(height: 12),
              _buildTextFormField(
                controller: _brandNameController,
                hintText: "Enter brand name",
                validator:
                    (value) =>
                        value?.isEmpty == true
                            ? "Please enter brand name"
                            : null,
              ),
              const SizedBox(height: 28),

              // Manufacturer Selection Section
              _buildManufacturerDropdown(),
              const SizedBox(height: 32),

              // Image Upload Section
              _buildImageUploadSection(selectedImage),
              const SizedBox(height: 40),

              // Submit Button
              _buildSubmitButton(),

              // Add bottom padding for better scrolling experience
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }
}
